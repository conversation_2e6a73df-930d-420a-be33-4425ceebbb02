<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外部异常统计</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f4f9;
            color: #333;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 80%;
            margin: 40px auto;
            background: #fff;
            border-radius: 8px;
            padding: 20px 30px;
        }

        h1 {
            text-align: center;
            color: #444;
        }

        .form-group {
            display: flex;
            align-items: center;
/*            justify-content: space-between;*/
            margin-bottom: 20px;
        }

        .form-group label {
            font-size: 16px;
            font-weight: bold;
            margin-right: 10px;
        }

        .form-group input {
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 45%;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            border-color: #007bff;
            outline: none;
        }

        button {
            display: block;
            margin: 0 auto;
            padding: 10px 20px;
            font-size: 16px;
            color: #fff;
            background-color: #007bff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #0056b3;
        }

        table {
            width: 100%;
            margin-top: 20px;
            border-collapse: collapse;
            background: #fff;
        }

        table thead {
            background: #007bff;
            color: #fff;
        }

        table th, table td {
            padding: 12px 15px;
            text-align: left;
            border: 1px solid #C0C0C0;
        }

        table th {
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
            text-align: center;
        }

        table tbody tr:nth-child(even) {
            background: #f4f4f9;
        }

        table tbody tr:hover {
            background: #eef;
        }

        .no-data {
            text-align: center;
            color: #888;
            font-style: italic;
            padding: 20px 0;
        }
        .no-underline {
            text-decoration: none;
            cursor: url('sword.png'), auto; /* 引用你的光标图像，如果无法加载则回退为默认 */
        }

    </style>
</head>

<body>
    <div class="container">
        <input id="proxyHost" type="hidden" value="127.0.0.1:3000">
        <div class="form-group">
            <div>
                <label for="startTime">开始时间:</label>
                <input type="text" id="startTime" placeholder="格式：YYYYMMDDHHmm">
            </div>
            <div>
                <label for="endTime">结束时间:</label>
                <input type="text" id="endTime" placeholder="格式：YYYYMMDDHHmm">
            </div>
            <div>
                <button onclick="queryData()">查询</button>
            </div>
        </div>
        <h2 style="margin-top: 30px;">查询结果</h2>
        <table>
            <thead>
                <tr>
                    <th style="width: 10%;" onclick="sortTable(0)">地区</th>
                    <th style="width: 30%;" onclick="sortTable(1)">接口</th>
                    <th style="width: 5%;" onclick="sortTable(2)">异常代码</th>
                    <th style="width: 50%;" onclick="sortTable(3)">异常提示</th>
                    <th style="width: 5%;" onclick="sortTable(4)">数量</th>
                </tr>
            </thead>
            <tbody id="resultTable">
                <tr class="no-data">
                    <td colspan="5">暂无数据</td>
                </tr>
            </tbody>
        </table>
    </div>
    <script>
    var proxyHost = document.getElementById("proxyHost").value;

    // 设置默认时间为当前时间的5分钟前
    function getDefaultTime() {
        const now = new Date();
        now.setMinutes(now.getMinutes() - 5);
        return now;
    }

    function formatDate(date) {
        const year = date.getFullYear();
        const month = ('0' + (date.getMonth() + 1)).slice(-2);
        const day = ('0' + date.getDate()).slice(-2);
        const hours = ('0' + date.getHours()).slice(-2);
        const minutes = ('0' + date.getMinutes()).slice(-2);
        return `${year}${month}${day}${hours}${minutes}`;
    }

    // 初始化默认时间
    const defaultTime = getDefaultTime();
    const startTime = formatDate(defaultTime);
    const endTime = formatDate(new Date());

    // 设置开始时间和结束时间的默认值
    document.getElementById('startTime').value = startTime;
    document.getElementById('endTime').value = endTime;

    function queryData() {
        if(!localStorage.getItem('2200')){
            const errorMessages  = {
                "2200": "手机号码不存在，您可以尝试使用证件号码登录",
                "1029": "初始化秘钥失败",
                "2007": "短信发送次数超限。",
                "2034": "未查询到您与该企业的关联关系信息，请确认录入的信息是否正确！",
                "2999": "服务器异常！",
                "4292": "请先完成扫脸认证",
                "2023": "连续认证错误次数过多，您的账号已被锁定。建议您直接使用“忘记密码”修改密码后重新登录或等待次日零时自动解锁。",
                "2628": "当前涉税专业服务机构的纳税人状态不允许登录，请恢复至正常或清算状态再进行登录",
                "2706": "图片拼图验证失败",
                "2035": "输入的个人账号或密码错误，请重新输入！",
                "2037": "登记企业信息不存在！",
                "2707": "图片拼图已失效",
                "2101": "请重新登录后继续办理业务！",
                "2036": "输入的短信验证码错误，请重新输入！还剩余4次",
                "2064": "已输入的个人账号或密码错误，请至电子税务局【自然人业务】登录页，点击找回密码 后，用新密码重新登录！",
                "2108": "二维码已被扫描！",
                "2501": "该用户信息不存在！",
                "2063": "已连续3次输入密码错误，您可以通过“忘记密码”设置新密码。",
                "2205": "应用令牌无效！",
                "2701": "您使用短信验证码过于频繁，请稍后再试。",
                "1019": "认证服务返回信息中不存在密钥信息",
                "2005": "验证码已失效！",
                "2060": "该用户已停用，如有疑问，请联系主管税务机关",
                "2059": "该用户未注册，请在自然人业务入口进行用户注册",
                "2062": "已连续4次输入个人用户密码错误，您可以通过“忘记密码”设置新密码。如果再次输入错误，您的账户会被锁定！",
                "2716": "没有指纹/面容开通记录！",
                "2061": "已连续3次输入个人用户密码错误，您可以通过“忘记密码”设置新密码。",
                "2018": "输入的统一社会信用代码或纳税人识别号错误，请重新输入",
                "2019": "身份认证已失效，请重新登录！",
                "2053": "您与该企业的绑定关系已过期，请联系企业管理人员！",
                "2311": "不允许切换为同一身份！",
                "1020": "请求报文验签失败"
            }
            // 遍历对象并写入localStorage
            for (const [key, value] of Object.entries(errorMessages)) {
                localStorage.setItem(key, value);
            }

        }


        const startTime = document.getElementById('startTime').value;
        const endTime = document.getElementById('endTime').value;
        const targetUrl = `https://warden.dc.servyou-it.com/warden/appAgg/getChartByMonitor.json?appCode=tax-gateway&targetType=APP&targetId=tax-gateway&monitorId=tax-gateway-outside&output=originCodeGroupCount&startTime=${startTime}&endTime=${endTime}`;
        const url = `http://${proxyHost}/proxy?url=` + encodeURIComponent(targetUrl);

        // 清空表格
        const tableBody = document.getElementById('resultTable');
        tableBody.innerHTML = '<tr class="no-data"><td colspan="5">加载中...</td></tr>';

        // 发送AJAX请求获取数据
        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.head.code === "00000000" && data.body.series.length > 0) {
                    const groups = data.body.series[0].groups;
                    renderGroups(groups);
                } else {
                    renderGroups([]);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert("请求出错！");
            });
    }

    function renderGroups(groups) {
        const tbody = document.getElementById('resultTable');
        tbody.innerHTML = ''; // 清空之前的表格数据

        if (groups.length === 0) {
            tbody.innerHTML = '<tr class="no-data"><td colspan="5">暂无数据</td></tr>';
            return;
        }
        var isFirst = true;
        groups.forEach(group => {
            const row = tbody.insertRow();
            row.id = group.value
            const areaCodeCell = row.insertCell(0);
            const proxyTypeCell = row.insertCell(1);
            const originErrorCodeCell = row.insertCell(2);
            const originErrorMsgCell = row.insertCell(3);
            const countCell = row.insertCell(4);

            const valurArr = group.value.split('.');
            areaCodeCell.textContent = valurArr[0];
            proxyTypeCell.textContent = valurArr[1];
            originErrorCodeCell.textContent = valurArr[2];
            const errorMsg = localStorage.getItem(valurArr[2]);
            originErrorMsgCell.textContent = errorMsg;


            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;
            const wardenShow = 'https://warden.dc.servyou-it.com/warden/warden_v2/log/query.html?a=tax-gateway&i=tax-gateway-preset&r=15&q=&fi=["1000areacode","' + valurArr[0] + '","1000proxyType","' + valurArr[1] + '","1000originErrorCode","' + valurArr[2] + '"]&o=@timestamp&ot=DESC';
            const aTag = document.createElement('a');
            aTag.href = wardenShow;
            aTag.target = '_blank';
            aTag.textContent = group.data.join(', ');
            aTag.className = 'no-underline';
            aTag.style.display = 'block'; // 将 a 标签设置为块级元素
            aTag.style.textDecoration = 'none'; // 移除下划线
            aTag.style.width = '100%';
            aTag.style.height = '100%';

            countCell.appendChild(aTag);
        });

        // 调用
        processTableRows(asyncProcessMsg, 200);

    }
    // 排序函数
    let sortOrder = [true, true]; // 用来标记每列的排序顺序，默认升序

    function sortTable(columnIndex) {
        const tbody = document.getElementById('resultTable');
        const rows = Array.from(tbody.rows);
        const isAscending = sortOrder[columnIndex];

        // 排序
        rows.sort((rowA, rowB) => {
            const cellA = rowA.cells[columnIndex].textContent.trim();
            const cellB = rowB.cells[columnIndex].textContent.trim();

            if (columnIndex === 0 || columnIndex === 1 || columnIndex === 3) { // 按 "Value" 排序
                return isAscending ? cellA.localeCompare(cellB) : cellB.localeCompare(cellA);
            } else { // 按 "Data" 排序
                const dataA = parseFloat(cellA.split(',')[0]);
                const dataB = parseFloat(cellB.split(',')[0]);
                return isAscending ? dataA - dataB : dataB - dataA;
            }
        });

        // 将排序后的行重新添加到表格
        rows.forEach(row => tbody.appendChild(row));

        // 更新排序顺序
        sortOrder[columnIndex] = !isAscending;
    }


    async function fetchLogMessages(rowId) {
        const startTime = document.getElementById('startTime').value;
        const endTime = document.getElementById('endTime').value;
        const valurArr = rowId.split('.');
        const areaCode = valurArr[0];
        const proxyType = valurArr[1];
        const originErrorCode = valurArr[2];

        const errorMsg = localStorage.getItem(originErrorCode);
        if (errorMsg) {
            return errorMsg;
        }


        const targetUrl = 'https://warden.dc.servyou-it.com/warden/searchLog/queryLog.json?indexId=tax-gateway-preset&query=@source:"/usr/local/logs/tax-gateway/common-net.log" AND areacode:' + areaCode + ' AND proxyType:' + proxyType + ' AND originErrorCode:' + originErrorCode + '&timeScale=*********&orderByField=@timestamp&orderByType=DESC&limit=5&userName=gufl&startTime=' + startTime + '000&endTime=' + endTime + '000';
        const url = `http://${proxyHost}/proxy?url=` + encodeURIComponent(targetUrl);


        try {
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                }
            });

            const data = await response.json();

            // Check if the response was successful
            if (data.head.code === "00000000" && data.body.logList) {
                const originErrorMsg = data.body.logList[0].fields.originErrorMsg;
                localStorage.setItem(originErrorCode, originErrorMsg);
                return originErrorMsg

                console.log("Fetched Error Messages:", errorMessages);
            } else {
                console.error("Error: " + data.head.description);
            }
        } catch (error) {
            console.error("Request failed", error);
        }
    }



    // 延迟函数：返回一个 Promise，延迟指定的毫秒数
    function delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 定义调用方法的函数
    async function processTableRows(method, delayMs) {
        const tbody = document.getElementById('resultTable');

        const rows = Array.from(tbody.rows);

        for (let row of rows) {
            if (row.cells[3].textContent) {
                continue;
            }
            const rowId = row.id; // 获取行的 ID
            console.log(`Calling method with row ID: ${rowId}`);
            // 调用传入的方法并传递行 ID
            await fetchLogMessages(rowId).then(res => {
                console.log("res:" + res);
                row.cells[3].textContent = res;
            });

            // 延迟指定时间
            await delay(delayMs);
        }
    }

    // 示例方法：模拟调用方法
    async function asyncProcessMsg(rowId) {
        console.log(`Processing row with ID: ${rowId}`);
        // 模拟异步操作
        return new Promise(resolve => setTimeout(() => resolve(`Done ${rowId}`), 100));
    }

    queryData();
    </script>
</body>

</html>