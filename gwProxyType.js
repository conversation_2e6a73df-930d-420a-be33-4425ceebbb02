const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const app = express();
const port = 6688;

// 解析 JSON 请求体
app.use(express.json({ limit: '10mb' })); // 设置为 10MB
app.use(express.urlencoded({ limit: '10mb', extended: true }));
// 启用 CORS
app.use(cors({
    origin: '*', // 允许所有来源访问
    methods: ['GET', 'POST', 'OPTIONS'], // 允许的 HTTP 方法
    allowedHeaders: ['Content-Type'] // 允许的请求头
}));



// 持久化文件路径
const keywordSuggestionsFilePath = path.join(__dirname, 'keywordSuggestions.json');
const updateJsonFilePath = path.join(__dirname, 'update.json');
const secretCodeFilePath = path.join(__dirname, 'secretCode.txt');

// 初始化 keywordSuggestions
let keywordSuggestions = {
    'PFTC_QUERY_SB_JKJG_XX': '社保费（4四合一）请求发起',
    'PFTC_QUERY_SB_JKJG_XX_CONSUMER': '社保费（4四合一）MQ消费',
    'FD_DECLARE_PAYMENT_RESULT_QUERY': '申报缴款结果6六合一',
    'FD_DECLARE_PAYMENT_RESULT_QUERY_CONVERT': '申报缴款结果6六合一（业务结果）',
    'SBF_CONVERT': '社保费数据回来会打印；如果社保费慢，则响应业务报文会有值=更新申报结果结果的响应业务报文',
    'FD_QUERY_QYXX': '企业信息',
    'FD_QUERY_QYXX_CONVERT': '企业信息（业务结果）',
    'FD_QUERY_ZHSB_LBCX': '已申报结果查询-列表查询',
    'FD_QUERY_CWBB_SBJG_LBCX': '法电列表查询-财务报表申报查询',
    'FD_QUERY_DSF_DBXX_WSBXX': '第三方代办信息-未申报信息',
    'FD_QUERY_DSF_WSBJGXX': '数字账户-未申报信息',
    'FD_THIRD_CXS_INIT': '法电-财行税-初始化',
    'FD_THIRD_CXS_SBBXX_QUERY': '法电-财行税未申报信息-查询 ',
    'FD_QUERY_WJKXX_NEW': '法电-未缴款信息查询(新)',
    'FD_QUERY_YJKXX_NEW': '法电-已缴款信息查询(新)',
    'FD_QUERY_SFZRDXX': '税费种认定信息查询',
    'FD_QUERY_CWZDBA': '财务制度备案信息查询',
    'FD_QUERY_CSCWKJZDKJBBZLDZBLIST': '法电-财务报表备案申报年报及对应报表-查询',
    'FD_QUERY_NSRZGXX': '获取纳税人资格信息',
    'FD_QUERY_NSRHDXX': '纳税人核定信息',
    'FD_SZZH_QUERY_SBMXCX': '数字账户-申报信息查询(申报补偿)',
    'FD_QUERY_CWBB_SBJGCX': '财务报表申报结果查询',
    'PFTC_LT_G_SBXXMX_SBF_NEW': '获取已申报人员明细(新)(参保信息)',
    '11111': '66666'
};

// 加载持久化的 keywordSuggestions 数据
try {
    if (fs.existsSync(keywordSuggestionsFilePath)) {
        const data = fs.readFileSync(keywordSuggestionsFilePath, 'utf8');
        keywordSuggestions = JSON.parse(data);
    } else {
        // 如果文件不存在，创建默认文件
        fs.writeFileSync(keywordSuggestionsFilePath, JSON.stringify(keywordSuggestions, null, 2), 'utf8');
    }
} catch (err) {
    console.error('Error loading keywordSuggestions.json:', err);
}

// 初始化 updateJson
let updateJson = {
    "version": "1.0",
    "remark": "测试",
    "download_url": "http://10.199.161.187:6688/download"
};

// 加载持久化的 updateJson 数据
try {
    if (fs.existsSync(updateJsonFilePath)) {
        const data = fs.readFileSync(updateJsonFilePath, 'utf8');
        updateJson = JSON.parse(data);
    } else {
        // 如果文件不存在，创建默认文件
        fs.writeFileSync(updateJsonFilePath, JSON.stringify(updateJson, null, 2), 'utf8');
    }
} catch (err) {
    console.error('Error loading update.json:', err);
}

// 初始化口令文件
const defaultSecretCode = '';
try {
    if (!fs.existsSync(secretCodeFilePath)) {
        // 如果文件不存在，创建默认口令文件
        fs.writeFileSync(secretCodeFilePath, defaultSecretCode, 'utf8');
        console.log('Created default secret code file');
    }
} catch (err) {
    console.error('Error initializing secret code file:', err);
}

// 定义 GET 接口
app.get('/keyword-suggestions', (req, res) => {
    res.json(keywordSuggestions);
});

app.get('/update', (req, res) => {
    const clientIp = req.ip;
    const username = req.query.username;
    const clientVersion = req.query.clientVersion;
    console.log(`USER-ip-name: ${clientIp} : ${username} : ${clientVersion}`);
    res.json(updateJson);
});

// 提供文件下载接口
app.get('/download', (req, res) => {
    const fileDirectory = path.join(__dirname, 'files');
    const fileName = 'Hammer.zip'; // 要提供下载的文件名
    const filePath = path.join(fileDirectory, fileName);

    // 检查文件是否存在
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            res.status(404).send('File not found');
            return;
        }

        // 设置响应头，告诉浏览器这是一个附件，需要下载
        res.download(filePath, fileName, (err) => {
            if (err) {
                console.error('Download error:', err);
                res.status(500).send('Download failed');
            }
        });
    });
});

// 更新 keywordSuggestions 的接口
app.post('/admin/keyword-suggestions', (req, res) => {
    const newKeywordSuggestions = req.body;

    if (!newKeywordSuggestions || typeof newKeywordSuggestions !== 'object') {
        return res.status(400).json({ error: 'Invalid JSON data' });
    }

    // 更新 keywordSuggestions
    keywordSuggestions = newKeywordSuggestions;

    // 将数据写入文件
    try {
        fs.writeFileSync(keywordSuggestionsFilePath, JSON.stringify(keywordSuggestions, null, 2), 'utf8');
        res.json({ message: 'Keyword suggestions updated successfully', data: keywordSuggestions });
    } catch (err) {
        console.error('Error writing keywordSuggestions.json:', err);
        res.status(500).json({ error: 'Failed to save keyword suggestions data' });
    }
});

// 更新 updateJson 的接口
app.post('/admin/update', (req, res) => {
    const { version, remark, download_url } = req.body;

    if (!version || !remark || !download_url) {
        return res.status(400).json({ error: 'Missing required fields' });
    }

    // 更新 updateJson
    updateJson = {
        version,
        remark,
        download_url
    };

    // 将数据写入文件
    try {
        fs.writeFileSync(updateJsonFilePath, JSON.stringify(updateJson, null, 2), 'utf8');
        res.json({ message: 'Update successful', data: updateJson });
    } catch (err) {
        console.error('Error writing update.json:', err);
        res.status(500).json({ error: 'Failed to save update data' });
    }
});

// 口令验证接口
app.post('/api/verify-secret-code', (req, res) => {
    const { code } = req.body;

    // 记录访问日志
    const clientIp = req.ip || req.connection.remoteAddress;
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Secret code verification attempt from IP: ${clientIp}`);

    // 验证入参
    if (!code || typeof code !== 'string') {
        console.log(`[${timestamp}] Invalid request: missing or invalid code parameter`);
        return res.status(400).json({
            success: false,
            error: 'Invalid request: code parameter is required and must be a string'
        });
    }

    try {
        // 读取服务端口令文件
        const serverSecretCode = fs.readFileSync(secretCodeFilePath, 'utf8').trim();
        if (!code.trim()){
            res.json({ success: false });
        } else {
            // 比较口令
            const isValid = code.trim() === serverSecretCode;

            if (isValid) {
                console.log(`[${timestamp}] Secret code verification successful from IP: ${clientIp}`);
                res.json({ success: true });
            } else {
                console.log(`[${timestamp}] Secret code verification failed from IP: ${clientIp} - Invalid code`);
                res.json({ success: false });
            }
        }
    } catch (err) {
        console.error(`[${timestamp}] Error reading secret code file:`, err);
        res.status(500).json({
            success: false,
            error: 'Internal server error: unable to verify code'
        });
    }
});

// 管理员更新口令接口
app.post('/admin/secret-code', (req, res) => {
    const { newCode } = req.body;

    // 记录访问日志
    const clientIp = req.ip || req.connection.remoteAddress;
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Secret code update attempt from IP: ${clientIp}`);

    // 验证入参
    if (!newCode || typeof newCode !== 'string' || newCode.trim().length < 6) {
        return res.status(400).json({
            error: 'Invalid request: newCode must be a string with at least 6 characters'
        });
    }

    try {
        // 写入新口令到文件
        fs.writeFileSync(secretCodeFilePath, newCode.trim(), 'utf8');
        console.log(`[${timestamp}] Secret code updated successfully from IP: ${clientIp}`);

        res.json({
            message: 'Secret code updated successfully',
            timestamp: timestamp
        });
    } catch (err) {
        console.error(`[${timestamp}] Error updating secret code file:`, err);
        res.status(500).json({
            error: 'Failed to update secret code'
        });
    }
});

// 管理后台页面
app.get('/admin', (req, res) => {
    const html = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Admin Panel</title>
            <style>
                textarea {
                    width: 100%;
                    height: 200px;
                    font-family: monospace;
                }
                .section {
                    margin-bottom: 30px;
                }
            </style>
        </head>
        <body>
            <h1>Admin Panel</h1>

            <!-- Keyword Suggestions Section -->
            <div class="section">
                <h2>Keyword Suggestions</h2>
                <form id="keywordSuggestionsForm">
                    <textarea id="keywordSuggestionsData">${JSON.stringify(keywordSuggestions, null, 2)}</textarea><br><br>
                    <button type="submit">Update Keyword Suggestions</button>
                </form>
            </div>

            <!-- Update JSON Section -->
            <div class="section">
                <h2>Update JSON</h2>
                <form id="updateForm">
                    <label for="version">Version:</label><br>
                    <input type="text" id="version" name="version" value="${updateJson.version}" required><br><br>

                    <label for="remark">Remark:</label><br>
                    <input type="text" id="remark" name="remark" value="${updateJson.remark}" required><br><br>

                    <label for="download_url">Download URL:</label><br>
                    <input type="text" id="download_url" name="download_url" value="${updateJson.download_url}" required><br><br>

                    <button type="submit">Update</button>
                </form>
            </div>

            <!-- Secret Code Management Section -->
            <div class="section">
                <h2>Secret Code Management</h2>
                <form id="secretCodeForm">
                    <label for="newSecretCode">New Secret Code:</label><br>
                    <input type="password" id="newSecretCode" name="newSecretCode" placeholder="Enter new secret code (min 6 characters)" required minlength="6"><br><br>

                    <label for="confirmSecretCode">Confirm Secret Code:</label><br>
                    <input type="password" id="confirmSecretCode" name="confirmSecretCode" placeholder="Confirm new secret code" required minlength="6"><br><br>

                    <button type="submit">Update Secret Code</button>
                </form>
                <div style="margin-top: 10px; padding: 10px; background-color: #f0f8ff; border-left: 4px solid #007bff;">
                    <strong>Note:</strong> The secret code is used for production environment access verification.
                    Make sure to use a strong password with at least 6 characters.
                </div>
            </div>

            <script>
                // Keyword Suggestions Form Submission
                document.getElementById('keywordSuggestionsForm').addEventListener('submit', async (e) => {
                    e.preventDefault();

                    const keywordSuggestionsData = document.getElementById('keywordSuggestionsData').value;

                    let parsedData;
                    try {
                        parsedData = JSON.parse(keywordSuggestionsData);
                    } catch (err) {
                        alert('Invalid JSON format');
                        return;
                    }

                    const response = await fetch('/admin/keyword-suggestions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(parsedData)
                    });

                    const result = await response.json();
                    if (response.ok) {
                        alert('Keyword suggestions updated successfully!');
                    } else {
                        alert('Error: ' + result.error);
                    }
                });

                // Update JSON Form Submission
                document.getElementById('updateForm').addEventListener('submit', async (e) => {
                    e.preventDefault();

                    const formData = {
                        version: document.getElementById('version').value,
                        remark: document.getElementById('remark').value,
                        download_url: document.getElementById('download_url').value
                    };

                    const response = await fetch('/admin/update', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });

                    const result = await response.json();
                    if (response.ok) {
                        alert('Update successful!');
                    } else {
                        alert('Error: ' + result.error);
                    }
                });

                // Secret Code Form Submission
                document.getElementById('secretCodeForm').addEventListener('submit', async (e) => {
                    e.preventDefault();

                    const newSecretCode = document.getElementById('newSecretCode').value;
                    const confirmSecretCode = document.getElementById('confirmSecretCode').value;

                    // 验证两次输入是否一致
                    if (newSecretCode !== confirmSecretCode) {
                        alert('Secret codes do not match. Please try again.');
                        return;
                    }

                    // 验证密码强度
                    if (newSecretCode.length < 6) {
                        alert('Secret code must be at least 6 characters long.');
                        return;
                    }

                    const response = await fetch('/admin/secret-code', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ newCode: newSecretCode })
                    });

                    const result = await response.json();
                    if (response.ok) {
                        alert('Secret code updated successfully!');
                        // 清空表单
                        document.getElementById('newSecretCode').value = '';
                        document.getElementById('confirmSecretCode').value = '';
                    } else {
                        alert('Error: ' + result.error);
                    }
                });
            </script>
        </body>
        </html>
    `;
    res.send(html);
});

// 启动服务器
app.listen(port, () => {
    console.log(`Server is running at http://localhost:${port}`);
});