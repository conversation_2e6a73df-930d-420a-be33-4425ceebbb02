<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8"/>
    <title>异步任务配置</title>
    <style>
        body {
            font-family: "Segoe UI", sans-serif;
            background-color: #f9f9f9;
            margin: 0;
            padding: 20px;
        }

        h2 {
            margin-bottom: 10px;
        }

        /* 页面布局 */
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        /* 固定的环境选择区域 */
        .fixed-header {
            flex-shrink: 0;
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
            padding: 20px;
            z-index: 100;
        }

        /* 可滚动的表格区域 */
        .scrollable-content {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            padding: 0 20px;
        }

        /* 阿里风格表格容器 */
        .table-container {
            flex: 1;
            overflow: auto;
            border: 1px solid #f0f0f0;
            border-radius: 6px;
            background: white;
            margin: 16px 0;
            position: relative;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        /* 阿里风格滚动条 */
        .table-container::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f5f5f5;
            border-radius: 3px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #d9d9d9;
            border-radius: 3px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #bfbfbf;
        }

        .table-container::-webkit-scrollbar-corner {
            background: #f5f5f5;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            table-layout: fixed;
            font-size: 14px;
        }

        th, td {
            text-align: left;
            padding: 12px 8px;
            border-bottom: 1px solid #f0f2f5;
            white-space: normal;
            word-wrap: break-word;
            position: relative;
            vertical-align: top;
        }

        /* 阿里风格表头 */
        th {
            background: #fafafa;
            color: rgba(0, 0, 0, 0.85);
            font-weight: 500;
            font-size: 14px;
            position: sticky;
            top: 0;
            z-index: 10;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.3s;
        }

        th:hover {
            background: #f5f5f5;
        }

        /* 表头分割线 */
        th:not(:last-child) {
            border-right: 1px solid #f0f0f0;
        }

        /* 阿里风格列样式 */
        .col-id {
            width: 5%;
            font-weight: 500;
            color: #1890ff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        .col-bizPartCode {
            width: 7%;
        }

        .col-bizPartName {
            width: 8%;
        }

        .col-bizCode {
            width: 7%;
        }

        .col-bizName {
            width: 8%;
        }

        .col-executeRuleContent {
            width: 10%;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.65);
        }

        .col-execType {
            width: 6%;
            text-align: center;
        }

        .col-mergeRuleContent {
            width: 8%;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.65);
        }

        .col-mergeTimeRange {
            width: 7%;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.65);
        }

        .col-retryRuleContent {
            width: 8%;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.65);
        }

        .col-taskCode {
            width: 8%;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            color: rgba(0, 0, 0, 0.65);
        }

        .col-createDate {
            width: 8%;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.65);
        }

        .col-modifyDate {
            width: 8%;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.65);
        }

        .col-isDelete {
            width: 5%;
            text-align: center;
        }

        /* 阿里风格表格行 */
        tbody tr {
            transition: background-color 0.3s;
            border-bottom: 1px solid #f0f0f0;
        }

        tbody tr:hover {
            background-color: #fafafa;
        }

        tbody tr:nth-child(even) {
            background-color: #fafafa;
        }

        tbody tr:nth-child(even):hover {
            background-color: #f5f5f5;
        }

        /* 阿里风格单元格 */
        td {
            color: rgba(0, 0, 0, 0.85);
            font-weight: 400;
            border-right: 1px solid #f0f0f0;
        }

        td:last-child {
            border-right: none;
        }

        /* 特殊列样式 */
        .col-id {
            font-weight: 600;
            color: #667eea;
            font-family: 'Courier New', monospace;
        }

        .col-status {
            text-align: center;
        }

        .col-date {
            color: #7f8c8d;
            font-size: 12px;
        }

        /* 阿里风格状态标签 */
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 400;
            line-height: 20px;
            border: 1px solid transparent;
        }

        .status-active {
            background: #f6ffed;
            border-color: #b7eb8f;
            color: #52c41a;
        }

        .status-inactive {
            background: #fff2f0;
            border-color: #ffccc7;
            color: #ff4d4f;
        }

        .status-pending {
            background: #fffbe6;
            border-color: #ffe58f;
            color: #faad14;
        }

        /* 操作按钮样式 */
        .action-buttons {
            display: flex;
            gap: 6px;
            justify-content: center;
        }

        .btn-action {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .btn-edit {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }

        .btn-edit:hover {
            background: linear-gradient(135deg, #0984e3 0%, #0770c4 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(9, 132, 227, 0.3);
        }

        .btn-copy {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
        }

        .btn-copy:hover {
            background: linear-gradient(135deg, #6c5ce7 0%, #5f3dc4 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(108, 92, 231, 0.3);
        }

        /* 阿里风格空数据状态 */
        .empty-state {
            text-align: center;
            padding: 48px 20px;
            color: rgba(0, 0, 0, 0.45);
        }

        .empty-state i {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.25;
            color: #d9d9d9;
        }

        .empty-state h3 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.65);
        }

        .empty-state p {
            margin: 0;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
        }

        /* 重新设计的环境选择器 */
        .env-selector-container {
            margin: 16px 0;
            padding: 16px 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }

        .env-selector-container.production-mode {
            background: linear-gradient(135deg, #fff2f0 0%, #ffebe8 100%);
            border-color: #ff7875;
            box-shadow: 0 2px 8px rgba(255, 77, 79, 0.15);
        }

        /* 环境选择主区域 */
        .env-main-section {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 12px;
        }

        .env-label-section {
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
        }

        .env-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
            background: #52c41a;
            transition: all 0.3s ease;
        }

        .env-icon.production {
            background: #ff4d4f;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 77, 79, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 77, 79, 0); }
        }

        .env-label-text {
            font-size: 16px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            transition: color 0.3s ease;
        }

        .env-label-text.production-mode {
            color: #ff4d4f;
        }

        /* 选择框区域 */
        .env-select-section {
            flex: 1;
            max-width: 300px;
            display: flex;
            justify-content: left;
            align-items: center;
        }

        .custom-select {
            position: relative;
            width: 100%;
        }

        #envSelect {
            width: 100%;
            height: 40px;
            padding: 0 40px 0 16px;
            border: 2px solid #d9d9d9;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            cursor: pointer;
            transition: all 0.3s ease;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        #envSelect:hover {
            border-color: #40a9ff;
            box-shadow: 0 2px 4px rgba(64, 169, 255, 0.1);
        }

        #envSelect:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
        }

        #envSelect.production-mode {
            border-color: #ff7875;
            background: #fff8f6;
        }

        #envSelect.production-mode:hover {
            border-color: #ff4d4f;
            box-shadow: 0 2px 4px rgba(255, 77, 79, 0.1);
        }

        #envSelect.production-mode:focus {
            border-color: #ff4d4f;
            box-shadow: 0 0 0 3px rgba(255, 77, 79, 0.1);
        }

        /* 警告区域 */
        .env-warning-section {
            display: none;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 16px;
            background: #fff2f0;
            border: 1px solid #ffccc7;
            border-radius: 6px;
            margin-top: 12px;
        }

        .env-warning-section.show {
            display: flex;
        }

        .env-warning-icon {
            font-size: 16px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-3px); }
            60% { transform: translateY(-2px); }
        }

        .env-warning-text {
            color: #ff4d4f;
            font-size: 13px;
            font-weight: 500;
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .env-main-section {
                flex-direction: column;
                gap: 12px;
                align-items: center;
                margin-bottom: 16px;
            }

            .env-label-section {
                justify-content: center;
                min-width: auto;
            }

            .env-select-section {
                max-width: 100%;
                width: 100%;
            }

            #envSelect {
                height: 36px;
                font-size: 13px;
            }

            .env-warning-section {
                padding: 10px 14px;
                margin-top: 16px;
            }

            .env-warning-text {
                font-size: 12px;
            }

            .fixed-header {
                padding: 16px 20px;
            }

            .scrollable-content {
                padding: 0 20px;
            }
        }

        @media (max-width: 480px) {
            .env-selector-container {
                padding: 12px 16px;
            }

            .env-main-section {
                gap: 10px;
                margin-bottom: 12px;
            }

            .env-label-text {
                font-size: 14px;
            }

            .env-icon {
                width: 20px;
                height: 20px;
                font-size: 12px;
            }

            #envSelect {
                height: 32px;
                font-size: 12px;
                padding: 0 32px 0 12px;
            }

            .env-warning-section {
                padding: 8px 12px;
                margin-top: 12px;
            }

            .env-warning-text {
                font-size: 11px;
            }

            .fixed-header {
                padding: 12px 20px;
            }

            .scrollable-content {
                padding: 0 20px;
            }
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 1200px;
            width: 95%;
            max-height: 85vh;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .form-grid {
            flex: 1;
            overflow-y: auto;
            padding-right: 10px;
            transition: all 0.3s ease;
        }

        /* 编辑现有记录时的表单样式 */
        .form-grid.editing-existing {
            background: linear-gradient(135deg, rgba(255, 243, 205, 0.1) 0%, rgba(255, 234, 167, 0.1) 100%);
            border: 1px solid rgba(243, 156, 18, 0.2);
            border-radius: 6px;
            padding: 15px;
            margin: 5px 0;
        }

        /* 创建新记录时的表单样式 */
        .form-grid.creating-new {
            background: linear-gradient(135deg, rgba(209, 236, 241, 0.1) 0%, rgba(168, 230, 207, 0.1) 100%);
            border: 1px solid rgba(76, 175, 80, 0.2);
            border-radius: 6px;
            padding: 15px;
            margin: 5px 0;
        }

        /* 滚动条样式 */
        .form-grid::-webkit-scrollbar {
            width: 6px;
        }

        .form-grid::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .form-grid::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .form-grid::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 表单网格布局 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            align-items: start;
        }

        .form-group {
            margin-bottom: 0;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .form-group textarea {
            flex: 1;
            min-height: 100px !important;
            height: 100px !important;
            max-height: 100px !important;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
            font-size: 14px;
        }

        /* 字段提示样式 */
        .field-hint {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
            font-weight: normal;
            line-height: 1.4;
            background-color: #f8f9fa;
            padding: 6px 8px;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }

        input[type="text"], textarea {
            width: 100%;
            padding: 10px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        input[type="text"]:focus, textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
        }

        textarea {
            resize: vertical;
            min-height: 80px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }



        .btn {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 70px;
        }

        .btn:hover {
            background-color: #45a049;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .btn-close {
            background-color: #6c757d;
        }

        .btn-close:hover {
            background-color: #5a6268;
        }

        .btn-copy {
            background-color: #17a2b8;
        }

        .btn-copy:hover {
            background-color: #138496;
        }

        /* 固定列样式 */
        td.sticky, th.sticky {
            position: sticky;
            left: 0;
            background-color: white;
            z-index: 2;
        }

        /* 第二列 */
        td.sticky + td, th.sticky + th {
            left: 70px; /* 根据实际宽度调整 */
        }

        /* 第三列 */
        td.sticky + td + td, th.sticky + th + th {
            left: 180px;
        }

        /* 第四列 */
        td.sticky + td + td + td, th.sticky + th + th + th {
            left: 280px;
        }

        /* 第五列 */
        td.sticky + td + td + td + td, th.sticky + th + th + th + th {
            left: 400px;
        }

        /* 所有输入框统一样式 */
        .form-group textarea {
            width: 100%;
            box-sizing: border-box;
            overflow-y: auto;
            resize: none;
        }

        /* 模态框标题样式 */
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0 0 25px 0;
            padding: 15px 20px;
            border-bottom: 2px solid #4CAF50;
            position: sticky;
            top: 0;
            background-color: white;
            z-index: 10;
            border-radius: 8px 8px 0 0;
        }

        /* 编辑现有记录的样式 */
        .modal-header.editing-existing {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-bottom: 2px solid #f39c12;
            border-left: 5px solid #e67e22;
            box-shadow: 0 2px 10px rgba(243, 156, 18, 0.2);
        }

        /* 创建新记录的样式 */
        .modal-header.creating-new {
            background: linear-gradient(135deg, #d1ecf1 0%, #a8e6cf 100%);
            border-bottom: 2px solid #4CAF50;
            border-left: 5px solid #27ae60;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }

        .modal-title {
            margin: 0;
            color: #333;
            font-size: 20px;
            font-weight: 600;
            flex: 1;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* 状态图标 */
        .status-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .status-icon.editing {
            background-color: #f39c12;
        }

        .status-icon.creating {
            background-color: #27ae60;
        }

        .modal-buttons {
            display: flex;
            gap: 10px;
            flex-shrink: 0;
        }

        /* 容器样式 */
        .custom-select {
            position: relative;
            display: inline-block;
            width: 200px;
        }

        /* 隐藏原生的 select 元素 */
        .custom-select select {
            display: block;
            width: 100%;
            padding: 10px 15px;
            font-size: 16px;
            color: #333;
            background-color: #fff;
            border: 1px solid #ccc;
            border-radius: 8px;
            appearance: none; /* 去除默认样式 */
            -webkit-appearance: none;
            -moz-appearance: none;
            outline: none;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        /* 自定义下拉箭头图标 */
        .custom-select::after {
            content: '▼';
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
            color: #999;
            font-size: 12px;
            transition: color 0.3s ease;
        }

        /* 悬停时的效果 */
        .custom-select:hover select {
            border-color: #888;
        }

        /* 聚焦时添加轮廓 */
        .custom-select select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
        }

        /* 可选：设置选项的样式（仅部分浏览器支持） */
        .custom-select select option {
            padding: 10px;
            background-color: #fff;
            color: #333;
        }

        /* 更改悬停下拉项的颜色 */
        .custom-select select option:hover {
            background-color: #f0f0f0;
        }
    </style>
</head>
<body>

<!-- 固定的环境选择区域 -->
<div class="fixed-header">
    <div class="env-selector-container" id="envContainer">
        <!-- 环境选择主区域 -->
        <div class="env-main-section">
            <div class="env-label-section">
                <div class="env-icon" id="envIcon">🌍</div>
                <span class="env-label-text" id="envLabel">环境选择</span>
            </div>
            <div class="env-select-section">
                <div class="custom-select">
                    <input id="proxyHost" type="hidden" value="localhost:3000">
                    <select id="envSelect">
                        <option value="tax-asynctask.cntax-test.sit.91lyd.com">cntax</option>
                        <option value="tax-asynctask.dntax-test.sit.91lyd.com">dntax</option>
                        <option value="tax-asynctask.rntax-test.sit.91lyd.com">rntax</option>
                        <option value="tax-asynctask.sntax-test.sit.91lyd.com">sntax</option>
                        <option value="tax-asynctask.ntax-test.sit.91lyd.com">ntax</option>
                        <option value="tax-asynctask.servyou-release.devops.91lyd.com">release</option>
                        <option value="pre">pre</option>
                        <option value="prod">prod</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 警告区域 -->
        <div class="env-warning-section" id="envWarning">
            <span class="env-warning-icon">⚠️</span>
            <span class="env-warning-text">您正在操作生产环境，请谨慎操作！所有更改将直接影响线上服务。</span>
        </div>
    </div>
</div>

<!-- 可滚动的表格区域 -->
<div class="scrollable-content">
    <div class="table-container">
        <table id="taskTable">
            <thead>
                <tr></tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>
</div>

<!-- 弹出框 -->
<div class="modal" id="editModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">编辑任务信息</h3>
            <div class="modal-buttons">
                <button class="btn" onclick="saveData()">保存</button>
                <button class="btn btn-copy" id="copyButton">复制</button>
                <button class="btn btn-close" onclick="closeModal()">关闭</button>
            </div>
        </div>
        <div class="form-grid" id="editForm"></div>
    </div>
</div>

<!-- 环境确认弹窗 -->
<div class="modal" id="envConfirmModal" style="display: none;">
    <div class="modal-content" style="max-width: 500px;">
        <div class="modal-header" style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); border-left: 5px solid #e67e22;">
            <h3 class="modal-title" style="color: #e67e22;">
                <span class="status-icon" style="background-color: #e67e22; color: white; width: 24px; height: 24px; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-right: 10px; font-size: 14px;">⚠</span>
                环境操作确认
            </h3>
        </div>
        <div style="padding: 20px;">
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin-bottom: 20px;">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <span style="color: #e67e22; font-size: 18px; margin-right: 8px;">⚠️</span>
                    <strong style="color: #e67e22;">警告：您即将在敏感环境中保存配置！</strong>
                </div>
                <p style="margin: 0; color: #856404;">
                    当前环境：<strong id="confirmEnvName" style="color: #e67e22;"></strong><br>
                    此操作将直接影响该环境的任务配置，请确认您的操作。
                </p>
            </div>
            <div style="background-color: #f8f9fa; border-radius: 6px; padding: 15px; margin-bottom: 20px;">
                <h6 style="margin: 0 0 10px 0; color: #495057;">📋 操作详情：</h6>
                <ul style="margin: 0; padding-left: 20px; color: #6c757d;">
                    <li>操作类型：<span id="confirmOperationType"></span></li>
                    <li>目标环境：<span id="confirmTargetEnv"></span></li>
                    <li>影响范围：任务配置数据</li>
                </ul>
            </div>
            <p style="color: #6c757d; font-size: 14px; margin: 0;">
                💡 <strong>建议：</strong>如果您是在测试功能，请切换到测试环境进行操作。
            </p>
        </div>
        <div style="display: flex; justify-content: center; gap: 15px; padding: 0 20px 20px 20px;">
            <button class="btn btn-close" onclick="closeEnvConfirmModal()" style="background-color: #6c757d;">
                <span style="margin-right: 5px;">❌</span> 取消操作
            </button>
            <button class="btn" onclick="confirmSave()" style="background-color: #e67e22;">
                <span style="margin-right: 5px;">✅</span> 确认保存
            </button>
        </div>
    </div>
</div>

<script>
    var proxyHost = document.getElementById("proxyHost").value;
    // 获取 select 元素
    const selectElement = document.getElementById('envSelect');
    selectElement.addEventListener('change', function () {
        fetchData();
    });

    // 获取环境选择的URL
    function getApi(path) {
        const envSelect = document.getElementById("envSelect");
        const selectedEnv = envSelect.value;
        const url = encodeURIComponent(`http://${selectedEnv}${path}`);
        return `http://${proxyHost}/proxy?url=${url}`;
    }

    async function getEnvIP(envDomain) {
        const lineupHostUrl = encodeURIComponent('http://phoenix.dc.servyou-it.com/phoenix/ops/getApplyOnlineCluster?appCode=tax-asynctask');
        const url = `http://${proxyHost}/proxy?url=${lineupHostUrl}`;
        try {
            console.log("url:" + url);
            const response = await fetch(url);
            const data = await response.json();

            // 根据传入的 envDomain 获取对应的 IP 地址
            const envData = data.body.filter(x => x.envDomain === envDomain)[0];
            if (envData && envData.clusterList[0] && envData.clusterList[0].k8sPodInfos[0]) {
                return envData.clusterList[0].k8sPodInfos[0].podIp;
            } else {
                throw new Error(`IP not found for ${envDomain}`);
            }
        } catch (error) {
            console.error(`Failed to get IP for ${envDomain}:`, error);
            return null;
        }
    }

    async function updateEnvSelect() {

        // 获取 pre 和 prod 的动态 IP 地址
        const preIp = await getEnvIP('pre');
        const prodIp = await getEnvIP('prod');
        console.log(preIp)
        console.log(prodIp)


        // 如果成功获取到 IP 地址，更新下拉框中的值
        if (preIp && prodIp) {
            const envSelect = document.getElementById('envSelect');
            const preOption = envSelect.querySelector('option[value="pre"]');
            const prodOption = envSelect.querySelector('option[value="prod"]');

            // 更新 pre 和 prod 的选项
            if (preOption) {
                preOption.value = preIp + ":8080";
                preOption.textContent = `Pre (${preIp})`;
            }
            if (prodOption) {
                prodOption.value = prodIp + ":8080";
                prodOption.textContent = `Prod (${prodIp})`;
            }
        }
    }


    let currentEditItem = null;

    // 字段提示配置
    const fieldHints = {
        'bizPartCode': '业务方代码 - 标识业务方的唯一代码',
        'bizPartName': '业务方名称 - 业务方的中文名称',
        'bizCode': '业务代码 - 具体业务的唯一标识代码',
        'bizName': '业务名称 - 具体业务的中文名称',
        'executeRuleContent': '逗号分隔1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m',
        'execType': '执行类型 - 0:预排队; 1:预执行; 2:已排队直接执行',
        'interfaceName': '业务执行接口 - 业务执行接口的beanName',
        'methodName': '业务执行方法 - 具体执行的方法名称',
        'paramType': '业务参数类型 - 业务参数的全限定类名',
        'taskCode': '任务代码 - 任务的唯一标识代码',
        'extend': '扩展信息 - JSON格式的扩展配置，如回调配置等',
        'mergeRuleContent': '任务合并依据 - 用于判断任务是否可合并的字段名',
        'mergeTimeRange': '合并时间范围 - 格式：地区:合并N秒内任务，如：BJ:300',
        'retryRuleContent': '重试规则 - 返回true表示可重试，不配置则不重试',
        'isDelete': '删除标识 - 0:未删除; 1:已删除'
    };

    window.onload = () => {
        fetchData();
        updateEnvSelect();
    };

    async function fetchData() {
        const tbody = document.querySelector('#taskTable tbody');
        tbody.innerHTML = '';
        try {
            const res = await fetch(getApi('/tax-asynctask/task/test/findAll'), {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({})
            });
            const data = await res.json();
            if (data.head && data.head.code === '00000000') {
                const rows = data.body || [];
                currentData = rows; // 保存当前数据
                if (rows.length > 0) {
                    const headers = Object.keys(rows[0]);
                    renderTable(headers, rows);
                } else {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="100%" class="empty-state">
                                <i>📋</i>
                                <h3>暂无数据</h3>
                                <p>当前环境下没有找到任何配置数据</p>
                            </td>
                        </tr>
                    `;
                }
            } else {
                alert('获取数据失败：' + data.head.description);
            }
        } catch (err) {
            console.error(err);
            alert('网络错误，请检查接口可用性。');
        }
    }

    function renderTable(headers, rows) {
        const theadRow = document.querySelector('#taskTable thead tr');
        const tbody = document.querySelector('#taskTable tbody');
        theadRow.innerHTML = '';
        tbody.innerHTML = '';

        // 定义要显示的列
        const displayColumns = [
            { key: 'id', label: 'ID', className: 'col-id' },
            { key: 'bizPartCode', label: '业务方代码', className: 'col-bizPartCode' },
            { key: 'bizPartName', label: '业务方名称', className: 'col-bizPartName' },
            { key: 'bizCode', label: '业务代码', className: 'col-bizCode' },
            { key: 'bizName', label: '业务名称', className: 'col-bizName' },
            { key: 'executeRuleContent', label: '执行规则', className: 'col-executeRuleContent' },
            { key: 'execType', label: '执行类型', className: 'col-execType' },
            { key: 'mergeRuleContent', label: '任务合并依据', className: 'col-mergeRuleContent' },
            { key: 'mergeTimeRange', label: '合并时间范围', className: 'col-mergeTimeRange' },
            { key: 'retryRuleContent', label: '重试规则', className: 'col-retryRuleContent' },
            { key: 'taskCode', label: '任务代码', className: 'col-taskCode' },
            { key: 'createDate', label: '创建时间', className: 'col-createDate' },
            { key: 'modifyDate', label: '修改时间', className: 'col-modifyDate' },
            { key: 'isDelete', label: '删除状态', className: 'col-isDelete' }
        ];

        // 构建表头
        displayColumns.forEach(col => {
            const th = document.createElement('th');
            th.textContent = col.label;
            th.className = col.className;
            theadRow.appendChild(th);
        });

        // 构建表格行
        rows.forEach(row => {
            const tr = document.createElement('tr');
            tr.setAttribute('data-id', row.id);
            tr.ondblclick = () => openEditModal(row);

            displayColumns.forEach(col => {
                const td = document.createElement('td');
                td.className = col.className;
                let value = row[col.key];

                // 特殊字段处理
                if (col.key === 'execType') {
                    let typeText = '';
                    let typeClass = 'pending';
                    switch(value) {
                        case 0: typeText = '预排队'; typeClass = 'pending'; break;
                        case 1: typeText = '预执行'; typeClass = 'active'; break;
                        case 2: typeText = '已排队直接执行'; typeClass = 'active'; break;
                        default: typeText = '未知'; typeClass = 'inactive';
                    }
                    td.innerHTML = `<span class="status-badge status-${typeClass}">${typeText}</span>`;
                } else if (col.key === 'isDelete') {
                    const statusText = value === 0 ? 'active' : 'inactive';
                    const statusLabel = value === 0 ? '正常' : '已删除';
                    td.innerHTML = `<span class="status-badge status-${statusText}">${statusLabel}</span>`;
                } else if (col.key === 'executeRuleContent') {
                    // 执行规则内容可能很长，需要换行显示
                    td.textContent = value !== null ? value.toString() : '';
                    td.style.maxHeight = '60px';
                    td.style.overflow = 'auto';
                } else if (col.key === 'mergeRuleContent') {
                    // 任务合并依据字段，可能为空
                    td.textContent = value !== null ? value.toString() : '-';
                    td.style.maxHeight = '60px';
                    td.style.overflow = 'auto';
                } else if (col.key === 'mergeTimeRange') {
                    // 合并时间范围，格式如：BJ:300
                    td.textContent = value !== null ? value.toString() : '-';
                } else if (col.key === 'retryRuleContent') {
                    // 重试规则内容，可能很长
                    td.textContent = value !== null ? value.toString() : '-';
                    td.style.maxHeight = '60px';
                    td.style.overflow = 'auto';
                } else if (col.key === 'createDate' || col.key === 'modifyDate') {
                    // 时间字段格式化显示
                    if (value) {
                        td.textContent = formatDate(value);
                    } else {
                        td.textContent = '-';
                    }
                } else {
                    td.textContent = value !== null ? value.toString() : '';
                }

                tr.appendChild(td);
            });

            tbody.appendChild(tr);
        });
    }

    function formatDate(isoString) {
        const date = new Date(isoString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}`;
    }

    // 编辑行功能
    function editRow(id) {
        const row = currentData.find(item => item.id === id);
        if (row) {
            openEditModal(row);
        }
    }

    // 复制行功能
    function copyRow(id) {
        const row = currentData.find(item => item.id === id);
        if (row) {
            const newRow = {...row};
            delete newRow.id;
            delete newRow.createDate;
            delete newRow.modifyDate;
            openEditModal(newRow);
        }
    }

    // 存储当前数据，用于操作按钮
    let currentData = [];

    function openEditModal(item) {
        currentEditItem = item;
        const form = document.getElementById('editForm');
        const modalTitle = document.querySelector('.modal-title');
        form.innerHTML = '';
        var hasIdField = false;
        var idValue = null;

        // 检查是否有 id 字段
        if (item.hasOwnProperty('id') && item.id !== null && item.id !== undefined) {
            hasIdField = true;
            idValue = item.id;
        }

        // 获取模态框头部和表单元素
        const modalHeader = document.querySelector('.modal-header');

        // 根据是否有id设置标题和样式
        if (hasIdField) {
            const bizName = item.bizName || '未知业务';

            // 设置编辑现有记录的样式
            modalHeader.className = 'modal-header editing-existing';
            form.className = 'form-grid editing-existing';

            // 创建状态图标和标题
            modalTitle.innerHTML = `
                <span class="status-icon editing">编</span>
                正在编辑【${idValue}】【${bizName}】配置
            `;
        } else {
            // 设置创建新记录的样式
            modalHeader.className = 'modal-header creating-new';
            form.className = 'form-grid creating-new';

            // 创建状态图标和标题
            modalTitle.innerHTML = `
                <span class="status-icon creating">新</span>
                正在编辑新配置
            `;
        }

        for (const key in item) {
            if (key === 'createDate' || key === 'modifyDate') continue;

            // 如果是原记录编辑且是id字段，跳过显示
            if (hasIdField && key === 'id') continue;

            const value = item[key];
            const div = document.createElement('div');
            div.className = 'form-group';

            const label = document.createElement('label');
            label.textContent = key;

            // 添加字段提示
            if (fieldHints[key]) {
                const hint = document.createElement('div');
                hint.className = 'field-hint';
                hint.innerHTML = fieldHints[key];
                div.appendChild(label);
                div.appendChild(hint);
            } else {
                div.appendChild(label);
            }

            // 创建统一的textarea输入框
            var input = document.createElement('textarea');
            input.name = key;

            if (key === 'extend') {
                try {
                    // 尝试解析 extend 字段的 JSON 字符串
                    const jsonObj = JSON.parse(value);
                    // 格式化 JSON 输出
                    input.value = JSON.stringify(jsonObj, null, 2);
                } catch (e) {
                    // 如果不是有效的 JSON 字符串，则直接显示原始值
                    input.value = value !== null ? value.toString() : '';
                }
            } else {
                input.value = value !== null ? value.toString() : '';
            }

            div.appendChild(input);
            form.appendChild(div);
        }

        // 控制复制按钮显示
        const copyButton = document.getElementById('copyButton');
        if (!hasIdField) {
            copyButton.style.display = 'none';
        } else {
            copyButton.style.display = 'inline-block';
        }

        // 显示模态框
        document.getElementById('editModal').style.display = 'flex';

        // 绑定复制按钮事件（移除之前的事件监听器避免重复绑定）
        copyButton.replaceWith(copyButton.cloneNode(true));
        const newCopyButton = document.getElementById('copyButton');
        newCopyButton.addEventListener('click', () => {
            if (!currentEditItem) return;
            const newItem = {...currentEditItem};
            delete newItem.id;
            openEditModal(newItem);
        });
    }



    function closeModal() {
        document.getElementById('editModal').style.display = 'none';
        currentEditItem = null;
    }

    // 点击模态框外部关闭
    document.getElementById('editModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            if (document.getElementById('editModal').style.display === 'flex') {
                closeModal();
            }
            if (document.getElementById('envConfirmModal').style.display === 'flex') {
                closeEnvConfirmModal();
            }
        }
    });

    // 点击环境确认弹窗外部关闭
    document.getElementById('envConfirmModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeEnvConfirmModal();
        }
    });

    // 存储待保存的数据，用于确认后保存
    let pendingSaveData = null;

    async function saveData() {
        // 检查当前环境是否需要确认
        const envSelect = document.getElementById('envSelect');
        const selectedEnv = envSelect.value;
        const selectedOption = envSelect.options[envSelect.selectedIndex];
        const envText = selectedOption.textContent;

        // 判断是否为敏感环境（pre或prod）
        const isSensitiveEnv = envText.toLowerCase().includes('pre') || envText.toLowerCase().includes('prod');

        if (isSensitiveEnv) {
            // 准备保存数据
            const form = document.getElementById('editForm');
            const inputs = form.querySelectorAll('textarea');
            const updatedData = {};

            // 如果是编辑原记录，需要包含id
            if (currentEditItem && currentEditItem.hasOwnProperty('id') && currentEditItem.id !== null && currentEditItem.id !== undefined) {
                updatedData.id = currentEditItem.id;
            }

            inputs.forEach(i => {
                if ((i.name != 'mergeTimeRange' && i.name != 'mergeRuleContent' ) && (i.value === null || i.value === '' || i.value === 'null')) return;

                if (i.name === 'extend') {
                    try {
                        // 解析 extend 字段的 JSON 字符串
                        const jsonObj = JSON.parse(i.value);
                        // 确保保存的是格式化的 JSON 字符串
                        updatedData[i.name] = JSON.stringify(jsonObj);
                    } catch (e) {
                        // 如果无法解析为 JSON，则直接保存原始值
                        updatedData[i.name] = i.value;
                    }
                } else {
                    updatedData[i.name] = i.value;
                }
            });

            // 存储待保存数据
            pendingSaveData = updatedData;

            // 显示确认弹窗
            showEnvConfirmModal(envText, updatedData.id ? '编辑现有配置' : '创建新配置');
            return;
        }

        // 非敏感环境直接保存
        await performSave();
    }

    function showEnvConfirmModal(envName, operationType) {
        document.getElementById('confirmEnvName').textContent = envName;
        document.getElementById('confirmOperationType').textContent = operationType;
        document.getElementById('confirmTargetEnv').textContent = envName;
        document.getElementById('envConfirmModal').style.display = 'flex';
    }

    function closeEnvConfirmModal() {
        document.getElementById('envConfirmModal').style.display = 'none';
        pendingSaveData = null;
    }

    async function confirmSave() {
        closeEnvConfirmModal();
        await performSave();
    }

    async function performSave() {
        if (!pendingSaveData) {
            // 如果没有待保存数据，重新收集数据（用于非敏感环境）
            const form = document.getElementById('editForm');
            const inputs = form.querySelectorAll('textarea');
            const updatedData = {};

            // 如果是编辑原记录，需要包含id
            if (currentEditItem && currentEditItem.hasOwnProperty('id') && currentEditItem.id !== null && currentEditItem.id !== undefined) {
                updatedData.id = currentEditItem.id;
            }

            inputs.forEach(i => {
                if ((i.name != 'mergeTimeRange' && i.name != 'mergeRuleContent' ) && (i.value === null || i.value === '' || i.value === 'null')) return;

                if (i.name === 'extend') {
                    try {
                        // 解析 extend 字段的 JSON 字符串
                        const jsonObj = JSON.parse(i.value);
                        // 确保保存的是格式化的 JSON 字符串
                        updatedData[i.name] = JSON.stringify(jsonObj);
                    } catch (e) {
                        // 如果无法解析为 JSON，则直接保存原始值
                        updatedData[i.name] = i.value;
                    }
                } else {
                    updatedData[i.name] = i.value;
                }
            });

            pendingSaveData = updatedData;
        }

        try {
            const res = await fetch(getApi('/tax-asynctask/task/test/updateConfig'), {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(pendingSaveData)
            });
            const result = await res.json();
            if (result.head && result.head.code === '00000000') {
                alert('保存成功');
                closeModal();
                fetchData(); // 刷新数据
            } else {
                alert('保存失败：' + result.head.description);
            }
        } catch (err) {
            console.error(err);
            alert('保存失败，请重试。');
        } finally {
            pendingSaveData = null;
        }
    }

    // 环境选择处理
    function handleEnvironmentChange() {
        const envSelect = document.getElementById('envSelect');
        const envContainer = document.getElementById('envContainer');
        const envLabel = document.getElementById('envLabel');
        const envIcon = document.getElementById('envIcon');
        const envWarning = document.getElementById('envWarning');

        const selectedValue = envSelect.value;
        const selectedText = envSelect.options[envSelect.selectedIndex].text;

        // 检查是否为生产环境
        const isProduction = selectedText.toLowerCase().includes('prod') || selectedText.toLowerCase().includes('pre');

        if (isProduction) {
            // 生产环境样式
            envContainer.classList.add('production-mode');
            envLabel.classList.add('production-mode');
            envSelect.classList.add('production-mode');
            envIcon.classList.add('production');
            envIcon.textContent = '⚠️';
            envWarning.classList.add('show');

            // 更新警告文本
            const warningText = envWarning.querySelector('.env-warning-text');
            if (selectedText.toLowerCase().includes('prod')) {
                warningText.textContent = '您正在操作生产环境，请谨慎操作！所有更改将直接影响线上服务。';
            } else {
                warningText.textContent = '您正在操作预生产环境，请谨慎操作！此环境接近生产环境配置。';
            }
        } else {
            // 测试环境样式
            envContainer.classList.remove('production-mode');
            envLabel.classList.remove('production-mode');
            envSelect.classList.remove('production-mode');
            envIcon.classList.remove('production');
            envIcon.textContent = '🌍';
            envWarning.classList.remove('show');
        }

        // 刷新数据
        fetchData();
    }

    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 绑定环境选择事件
        document.getElementById('envSelect').addEventListener('change', handleEnvironmentChange);

        // 初始化环境状态
        handleEnvironmentChange();
    });
</script>

</body>
</html>