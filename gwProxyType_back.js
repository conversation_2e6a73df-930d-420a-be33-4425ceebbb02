const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const cookieParser = require('cookie-parser'); // 引入 cookie-parser
const app = express();
const port = 6688;

// 解析 JSON 请求体
app.use(express.json({ limit: '10mb' })); // 设置为 10MB
app.use(express.urlencoded({ limit: '10mb', extended: true }));
// 启用 CORS
app.use(cors({
    origin: '*', // 允许所有来源访问
    methods: ['GET', 'POST', 'OPTIONS'], // 允许的 HTTP 方法
    allowedHeaders: ['Content-Type'] // 允许的请求头
}));
app.use(cookieParser()); // 使用 cookie-parser 中间件

// 持久化文件路径
const keywordSuggestionsFilePath = path.join(__dirname, 'keywordSuggestions.json');
const updateJsonFilePath = path.join(__dirname, 'update.json');

// 初始化 keywordSuggestions
let keywordSuggestions = {
    'PFTC_QUERY_SB_JKJG_XX': '社保费（4四合一）请求发起',
    'PFTC_QUERY_SB_JKJG_XX_CONSUMER': '社保费（4四合一）MQ消费',
    'FD_DECLARE_PAYMENT_RESULT_QUERY': '申报缴款结果6六合一',
    'FD_DECLARE_PAYMENT_RESULT_QUERY_CONVERT': '申报缴款结果6六合一（业务结果）',
    'SBF_CONVERT': '社保费数据回来会打印；如果社保费慢，则响应业务报文会有值=更新申报结果结果的响应业务报文',
    'FD_QUERY_QYXX': '企业信息',
    'FD_QUERY_QYXX_CONVERT': '企业信息（业务结果）',
    'FD_QUERY_ZHSB_LBCX': '已申报结果查询-列表查询',
    'FD_QUERY_CWBB_SBJG_LBCX': '法电列表查询-财务报表申报查询',
    'FD_QUERY_DSF_DBXX_WSBXX': '第三方代办信息-未申报信息',
    'FD_QUERY_DSF_WSBJGXX': '数字账户-未申报信息',
    'FD_THIRD_CXS_INIT': '法电-财行税-初始化',
    'FD_THIRD_CXS_SBBXX_QUERY': '法电-财行税未申报信息-查询 ',
    'FD_QUERY_WJKXX_NEW': '法电-未缴款信息查询(新)',
    'FD_QUERY_YJKXX_NEW': '法电-已缴款信息查询(新)',
    'FD_QUERY_SFZRDXX': '税费种认定信息查询',
    'FD_QUERY_CWZDBA': '财务制度备案信息查询',
    'FD_QUERY_CSCWKJZDKJBBZLDZBLIST': '法电-财务报表备案申报年报及对应报表-查询',
    'FD_QUERY_NSRZGXX': '获取纳税人资格信息',
    'FD_QUERY_NSRHDXX': '纳税人核定信息',
    'FD_SZZH_QUERY_SBMXCX': '数字账户-申报信息查询(申报补偿)',
    'FD_QUERY_CWBB_SBJGCX': '财务报表申报结果查询',
    'PFTC_LT_G_SBXXMX_SBF_NEW': '获取已申报人员明细(新)(参保信息)',
    '11111': '66666'
};

// 加载持久化的 keywordSuggestions 数据
try {
    if (fs.existsSync(keywordSuggestionsFilePath)) {
        const data = fs.readFileSync(keywordSuggestionsFilePath, 'utf8');
        keywordSuggestions = JSON.parse(data);
    } else {
        // 如果文件不存在，创建默认文件
        fs.writeFileSync(keywordSuggestionsFilePath, JSON.stringify(keywordSuggestions, null, 2), 'utf8');
    }
} catch (err) {
    console.error('Error loading keywordSuggestions.json:', err);
}

// 初始化 updateJson
let updateJson = {
    "version": "1.0",
    "remark": "测试",
    "download_url": "http://10.199.161.187:6688/download"
};

// 加载持久化的 updateJson 数据
try {
    if (fs.existsSync(updateJsonFilePath)) {
        const data = fs.readFileSync(updateJsonFilePath, 'utf8');
        updateJson = JSON.parse(data);
    } else {
        // 如果文件不存在，创建默认文件
        fs.writeFileSync(updateJsonFilePath, JSON.stringify(updateJson, null, 2), 'utf8');
    }
} catch (err) {
    console.error('Error loading update.json:', err);
}

// 定义 GET 接口
app.get('/keyword-suggestions', (req, res) => {
    // 获取用户本地存储的 keywordSuggestions
    const userKeywordSuggestions = req.cookies.userKeywordSuggestions ? JSON.parse(decodeURIComponent(req.cookies.userKeywordSuggestions)) : {};

    // 移除 keywordSuggestions 中与 userKeywordSuggestions 重复的 key
    const filteredKeywordSuggestions = Object.keys(keywordSuggestions)
        .filter(key => !userKeywordSuggestions.hasOwnProperty(key))
        .reduce((obj, key) => {
            obj[key] = keywordSuggestions[key];
            return obj;
        }, {});

    // 合并用户数据和过滤后的公共数据，用户数据优先并放在最前面
    const mergedKeywordSuggestions = { ...userKeywordSuggestions, ...filteredKeywordSuggestions };
    res.json(mergedKeywordSuggestions);
});

// 管理后台页面
app.get('/admin', (req, res) => {
    const html = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Admin Panel</title>
            <style>
                textarea {
                    width: 100%;
                    height: 200px;
                    font-family: monospace;
                }
                .section {
                    margin-bottom: 30px;
                }
            </style>
        </head>
        <body>
            <h1>Admin Panel</h1>

            <!-- Public Keyword Suggestions Section -->
            <div class="section">
                <h2>Public Keyword Suggestions</h2>
                <form id="publicKeywordSuggestionsForm">
                    <textarea id="publicKeywordSuggestionsData">${JSON.stringify(keywordSuggestions, null, 2)}</textarea><br><br>
                    <button type="submit">Update Public Keyword Suggestions</button>
                </form>
            </div>

            <!-- User Keyword Suggestions Section -->
            <div class="section">
                <h2>User Keyword Suggestions</h2>
                <form id="userKeywordSuggestionsForm">
                    <textarea id="userKeywordSuggestionsData">${JSON.stringify(req.cookies.userKeywordSuggestions ? JSON.parse(decodeURIComponent(req.cookies.userKeywordSuggestions)) : {}, null, 2)}</textarea><br><br>
                    <button type="submit">Update User Keyword Suggestions</button>
                </form>
            </div>

            <!-- Update JSON Section -->
            <div class="section">
                <h2>Update JSON</h2>
                <form id="updateForm">
                    <label for="version">Version:</label><br>
                    <input type="text" id="version" name="version" value="${updateJson.version}" required><br><br>

                    <label for="remark">Remark:</label><br>
                    <input type="text" id="remark" name="remark" value="${updateJson.remark}" required><br><br>

                    <label for="download_url">Download URL:</label><br>
                    <input type="text" id="download_url" name="download_url" value="${updateJson.download_url}" required><br><br>

                    <button type="submit">Update</button>
                </form>
            </div>

            <script>
                // Public Keyword Suggestions Form Submission
                document.getElementById('publicKeywordSuggestionsForm').addEventListener('submit', async (e) => {
                    e.preventDefault();

                    const publicKeywordSuggestionsData = document.getElementById('publicKeywordSuggestionsData').value;

                    let parsedData;
                    try {
                        parsedData = JSON.parse(publicKeywordSuggestionsData);
                    } catch (err) {
                        alert('Invalid JSON format');
                        return;
                    }

                    const response = await fetch('/admin/keyword-suggestions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(parsedData)
                    });

                    const result = await response.json();
                    if (response.ok) {
                        alert('Public keyword suggestions updated successfully!');
                    } else {
                        alert('Error: ' + result.error);
                    }
                });

                // User Keyword Suggestions Form Submission
                document.getElementById('userKeywordSuggestionsForm').addEventListener('submit', async (e) => {
                    e.preventDefault();

                    const userKeywordSuggestionsData = document.getElementById('userKeywordSuggestionsData').value;

                    let parsedData;
                    try {
                        parsedData = JSON.parse(userKeywordSuggestionsData);
                    } catch (err) {
                        alert('Invalid JSON format');
                        return;
                    }

                    // 将用户数据存储到 cookie 中，并设置有效期为一年
                    document.cookie = 'userKeywordSuggestions=' + encodeURIComponent(JSON.stringify(parsedData)) + '; path=/; max-age=31536000; SameSite=Lax';

                    alert('User keyword suggestions updated successfully!');
                });

                // Update JSON Form Submission
                document.getElementById('updateForm').addEventListener('submit', async (e) => {
                    e.preventDefault();

                    const formData = {
                        version: document.getElementById('version').value,
                        remark: document.getElementById('remark').value,
                        download_url: document.getElementById('download_url').value
                    };

                    const response = await fetch('/admin/update', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });

                    const result = await response.json();
                    if (response.ok) {
                        alert('Update successful!');
                    } else {
                        alert('Error: ' + result.error);
                    }
                });
            </script>
        </body>
        </html>
    `;
    res.send(html);
});

// 提供文件下载接口
app.get('/download', (req, res) => {
    const fileDirectory = path.join(__dirname, 'files');
    const fileName = 'QianJi.zip'; // 要提供下载的文件名
    const filePath = path.join(fileDirectory, fileName);

    // 检查文件是否存在
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            res.status(404).send('File not found');
            return;
        }

        // 设置响应头，告诉浏览器这是一个附件，需要下载
        res.download(filePath, fileName, (err) => {
            if (err) {
                console.error('Download error:', err);
                res.status(500).send('Download failed');
            }
        });
    });
});

// 更新 keywordSuggestions 的接口（仅更新公共数据）
app.post('/admin/keyword-suggestions', (req, res) => {
    const newKeywordSuggestions = req.body;

    if (!newKeywordSuggestions || typeof newKeywordSuggestions !== 'object') {
        return res.status(400).json({ error: 'Invalid JSON data' });
    }

    // 更新公共 keywordSuggestions
    keywordSuggestions = newKeywordSuggestions;

    // 将数据写入文件
    try {
        fs.writeFileSync(keywordSuggestionsFilePath, JSON.stringify(keywordSuggestions, null, 2), 'utf8');
        res.json({ message: 'Keyword suggestions updated successfully', data: keywordSuggestions });
    } catch (err) {
        console.error('Error writing keywordSuggestions.json:', err);
        res.status(500).json({ error: 'Failed to save keyword suggestions data' });
    }
});

// 更新 updateJson 的接口
app.post('/admin/update', (req, res) => {
    const { version, remark, download_url } = req.body;

    if (!version || !remark || !download_url) {
        return res.status(400).json({ error: 'Missing required fields' });
    }

    // 更新 updateJson
    updateJson = {
        version,
        remark,
        download_url
    };

    // 将数据写入文件
    try {
        fs.writeFileSync(updateJsonFilePath, JSON.stringify(updateJson, null, 2), 'utf8');
        res.json({ message: 'Update successful', data: updateJson });
    } catch (err) {
        console.error('Error writing update.json:', err);
        res.status(500).json({ error: 'Failed to save update data' });
    }
});

// 启动服务器
app.listen(port, () => {
    console.log(`Server is running at http://localhost:${port}`);
});