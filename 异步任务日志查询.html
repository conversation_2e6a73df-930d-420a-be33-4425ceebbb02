<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>异步任务日志查询</title>
    <link href="./bootstrap.min.css" rel="stylesheet">
    <style>
        body { margin: 0; overflow: hidden; height: 100vh; }
        .table-container { margin: 20px auto; width: 95%; max-height: calc(100vh - 200px); overflow-y: auto; border: 1px solid #ddd; }
        .custom-container { width: 95%; margin: 0 auto; }
        .table { width: 100%; table-layout: fixed; } /* 固定表格布局，防止超宽 */
        thead th { position: sticky; top: 0; background-color: #f8f9fa; z-index: 10; padding: 8px 16px; white-space: nowrap; }
        .table th, .table td { text-align: left; padding: 8px 16px; word-break: break-all; white-space: normal; }
        .event-column { width: 42%; min-width: 200px; word-break: break-all; white-space: pre-line; }
        /* 其他列宽度适当调整 */
        .time-column { width: 9%; }
        .taskid-column { width: 7%; }
        .traceid-column { width: 13%; }
        .stepname-column { width: 13%; }
        .sourcehost-column { width: 8%; }
        .clustername-column { width: 8%; }
        .loading-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; justify-content: center; align-items: center; }
        .spinner { width: 80px; height: 80px; border: 8px solid rgba(255,255,255,0.3); border-top-color: white; border-radius: 50%; animation: spin 1s linear infinite; }
        @keyframes spin { to { transform: rotate(360deg); } }
        /* stepName列专属背景色 */
        td.stepname-bg-lk { background-color: #ADD8E6 !important; }
        td.stepname-bg-hb { background-color: #ADD8E6 !important; }
        td.stepname-bg-cj { background-color: #ADD8E6 !important; }
        td.stepname-bg-dd { background-color: #90EE90 !important; }
        td.stepname-bg-ddmq { background-color: #6B8E23 !important; color: #fff; }
        td.stepname-bg-pdmq { background-color: #FFD700 !important; }
        td.stepname-bg-ywzx { background-color: #FFA500 !important; }
        td.stepname-bg-yb { background-color: #ec2f4b !important; }
        td.stepname-bg-jg { background-color: #ec2f4b !important; }
        td.stepname-bg-jgsend { background-color: #ec2f4b !important; }
        td.stepname-bg-zd { background-color: #FFB6C1 !important; }
        /* 保证hover时也有背景色 */
        tr.step-bg-lk:hover > td, tr.step-bg-hb:hover > td, tr.step-bg-cj:hover > td, tr.step-bg-dd:hover > td, tr.step-bg-ddmq:hover > td, tr.step-bg-pdmq:hover > td, tr.step-bg-ywzx:hover > td, tr.step-bg-yb:hover > td, tr.step-bg-jg:hover > td, tr.step-bg-jgsend:hover > td, tr.step-bg-zd:hover > td {
            filter: brightness(0.95);
        }
        /* traceId列莫兰迪色系背景色 */
        td.traceid-bg-0 { background-color: #A3B1BF !important; color: #222; }
        td.traceid-bg-1 { background-color: #B7AFA3 !important; color: #222; }
        td.traceid-bg-2 { background-color: #B5C4B1 !important; color: #222; }
        td.traceid-bg-3 { background-color: #D6C6B8 !important; color: #222; }
        td.traceid-bg-4 { background-color: #C1B2AB !important; color: #222; }
        td.traceid-bg-5 { background-color: #B7B6C1 !important; color: #222; }
        td.traceid-bg-6 { background-color: #A7A1AE !important; color: #222; }
        td.traceid-bg-7 { background-color: #B0A1A1 !important; color: #222; }
        td.traceid-bg-8 { background-color: #A1B0A7 !important; color: #222; }
        td.traceid-bg-9 { background-color: #B1A1B0 !important; color: #222; }
        td.traceid-bg-10 { background-color: #A1B1B0 !important; color: #222; }
        td.traceid-bg-11 { background-color: #B0B1A1 !important; color: #222; }
        /* 禁止横向滚动 */
        html, body { overflow-x: hidden; }
    </style>
</head>
<body>
    <div class="custom-container container-fluid mt-4">
        <h2>异步任务日志查询</h2>
        <div class="row mb-3">
            <div class="col">
                <label for="env" class="form-label">环境</label>
                <div class="dropdown">
                    <button class="btn btn-secondary dropdown-toggle" type="button" id="envDropdown" data-bs-toggle="dropdown" aria-expanded="false" style="width: 100%;background-color: white;color: black;">prod</button>
                    <ul class="dropdown-menu" aria-labelledby="envDropdown" style="width: 100%;text-align: center;min-width: 100%; ">
                        <li><a class="dropdown-item" href="#" onclick="selectEnv('prod')">prod</a></li>
                        <li><a class="dropdown-item" href="#" onclick="selectEnv('pre')">pre</a></li>
                        <li><a class="dropdown-item" href="#" onclick="selectEnv('release')">release</a></li>
                        <li><a class="dropdown-item" href="#" onclick="selectEnv('test')">test</a></li>
                    </ul>
                </div>
            </div>
            <div class="col">
                <label for="traceId" class="form-label">TraceId</label>
                <input type="text" class="form-control" id="traceId">
            </div>
            <div class="col">
                <label for="taskId" class="form-label">TaskId</label>
                <input type="text" class="form-control" id="taskId">
            </div>
            <div class="col">
                <label for="startTime" class="form-label">时间起</label>
                <input type="datetime-local" class="form-control" id="startTime">
            </div>
            <div class="col">
                <label for="endTime" class="form-label">时间止</label>
                <input type="datetime-local" class="form-control" id="endTime">
            </div>
            <div class="col">
                <label for="operate" class="form-label">操作</label>
                <div class="col-auto align-self-end"><button id="queryButton" class="btn btn-primary">查询</button></div>
            </div>
        </div>
    </div>
    <div class="table-container">
        <table class="table table-hover">
            <thead class="thead-light">
                <tr>
                    <th class="time-column">时间</th>
                    <th class="taskid-column">taskId</th>
                    <th class="traceid-column">traceId</th>
                    <th class="stepname-column">stepName</th>
                    <th class="event-column">event</th>
                    <th class="sourcehost-column">sourceHost</th>
                    <th class="clustername-column">clusterName</th>
                </tr>
            </thead>
            <tbody id="log-data">
                <!-- 数据行由JS渲染 -->
            </tbody>
        </table>
    </div>
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="spinner"></div>
    </div>
    <script src="./bootstrap.bundle.min.js"></script>
    <script>
    function selectEnv(env) {
        document.getElementById('envDropdown').textContent = env;
    }
    let wardenDomainMap = new Map();
    wardenDomainMap.set('prod', 'https://warden.dc.servyou-it.com');
    wardenDomainMap.set('pre', 'https://warden.dc.servyou-it.com');
    wardenDomainMap.set('release', 'http://warden-eyes-front.servyou-stable.sit.91lyd.com');
    wardenDomainMap.set('test', 'http://warden-eyes-front.servyou-stable.sit.91lyd.com');
    const logTableBody = document.getElementById('log-data');
    function formatTimestamp(ts) {
        const date = new Date(ts);
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0');
        const d = String(date.getDate()).padStart(2, '0');
        const h = String(date.getHours()).padStart(2, '0');
        const min = String(date.getMinutes()).padStart(2, '0');
        const s = String(date.getSeconds()).padStart(2, '0');
        const ms = String(date.getMilliseconds()).padStart(3, '0');
        return `${y}-${m}-${d} ${h}:${min}:${s}.${ms}`;
    }
    function showLoading() { document.getElementById("loadingOverlay").style.display = "flex"; }
    function hideLoading() { document.getElementById("loadingOverlay").style.display = "none"; }
    // stepName与颜色class映射
    const stepNameColorMap = {
        '创建任务-任务落库': 'stepname-bg-lk',
        '创建任务-合并任务': 'stepname-bg-hb',
        '创建任务': 'stepname-bg-cj',
        '任务调度': 'stepname-bg-dd',
        '任务调度MQ消费': 'stepname-bg-ddmq',
        '排队MQ消费': 'stepname-bg-pdmq',
        '业务执行': 'stepname-bg-ywzx',
        '任务执行异步回调结果': 'stepname-bg-yb',
        '任务执行结果通知': 'stepname-bg-jg',
        '任务执行结果通知-发送': 'stepname-bg-jgsend',
        '状态更新记录日志': 'stepname-bg-zd',
    };
    const stepBgClassList = Object.values(stepNameColorMap);
    let stepNameColorCount = stepBgClassList.length;
    function getStepBgClass(stepName) {
        // 标准化stepName
        const key = (stepName || '').trim();
        if (!key) return 'stepname-bg-lk';
        if (!(key in stepNameColorMap)) {
            // 未指定的stepName自动分配高对比色
            stepNameColorMap[key] = stepBgClassList[stepNameColorCount % stepBgClassList.length];
            stepNameColorCount++;
        }
        return stepNameColorMap[key];
    }
    // traceId到莫兰迪色class的映射，健壮防错
    const morandiColors = [
        'traceid-bg-0','traceid-bg-1','traceid-bg-2','traceid-bg-3','traceid-bg-4','traceid-bg-5','traceid-bg-6','traceid-bg-7','traceid-bg-8','traceid-bg-9','traceid-bg-10','traceid-bg-11'
    ];
    const traceIdColorMap = {};
    let traceIdColorIdx = 0;
    function getTraceIdBgClass(traceId) {
        if (!traceId || typeof traceId !== 'string' || !traceId.trim()) return '';
        if (!(traceId in traceIdColorMap)) {
            traceIdColorMap[traceId] = morandiColors[traceIdColorIdx % morandiColors.length];
            traceIdColorIdx++;
        }
        return traceIdColorMap[traceId];
    }
    function renderData(data) {
        logTableBody.innerHTML = '';
        // stepName优先级映射
        const stepOrder = [
            '任务调度MQ消费',
            '任务调度',
            '创建任务',
            '创建任务-合并任务',
            '创建任务-任务落库'
        ];
        function getStepOrder(stepName) {
            const idx = stepOrder.indexOf(stepName);
            return idx === -1 ? 999 : idx;
        }
        // 按@timestamp倒序，若相同则按stepName优先级
        data.sort((a, b) => {
            const t1 = b.fields['@timestamp'] - a.fields['@timestamp'];
            if (t1 !== 0) return t1;
            return getStepOrder(a.fields.stepName) - getStepOrder(b.fields.stepName);
        });
        data.forEach(log => {
            const fields = log.fields;
            const row = document.createElement('tr');
            const stepClass = getStepBgClass(fields.stepName);
            const traceClass = getTraceIdBgClass(fields.traceId);
            row.innerHTML = `
                <td>${formatTimestamp(fields['@timestamp'])}</td>
                <td>${fields.taskId ? fields.taskId : '&gt;&gt;&gt;&gt;&gt;&gt;'}</td>
                <td class="${traceClass}">${fields.traceId || ''}</td>
                <td class="${stepClass}">${fields.stepName || ''}</td>
                <td class="event-column">${fields.msg ? fields.msg.replace(/</g, '&lt;').replace(/>/g, '&gt;') : ''}</td>
                <td><a href='http://phoenix.dc.servyou-it.com/v2/#/ssh/terminal?appCode=tax-asynctask&podName=${fields['@sourceHost']}' target="_blank">${fields['@sourceHost'] || ''}</a></td>
                <td>${fields['@clusterName'] || ''}</td>
            `;
            logTableBody.appendChild(row);
        });
        hideLoading();
    }
    function formatyyyyMMddHHmmss(timestamp) {
        const date = new Date(timestamp);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}${month}${day}${hours}${minutes}${seconds}`;
    }
    // 默认时间
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
    const fiveMinutesLater = new Date(now.getTime() + 5 * 60 * 1000);
    const options = { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', hour12: false };
    document.getElementById('startTime').value = fiveMinutesAgo.toLocaleString('sv-SE', options).replace(' ', 'T');
    document.getElementById('endTime').value = fiveMinutesLater.toLocaleString('sv-SE', options).replace(' ', 'T');
    document.getElementById('queryButton').addEventListener('click', () => {
        logTableBody.innerHTML = '';
        showLoading();
        const traceId = document.getElementById('traceId').value.trim();
        const taskId = document.getElementById('taskId').value.trim();
        const startTime = formatyyyyMMddHHmmss(new Date(document.getElementById('startTime').value));
        const endTime = formatyyyyMMddHHmmss(new Date(document.getElementById('endTime').value + ':59.999'));
        const env = document.getElementById('envDropdown').textContent.trim();
        let query = '@source:"/usr/local/logs/tax-asynctask/common-schedule.log" AND @sourceHost:*' + env + '*';
        if (taskId) query += ' AND taskId:"' + taskId + '"';
        if (traceId) query += ' AND traceId:"' + traceId + '"';
        const wardenTargetUrl = encodeURIComponent(`${wardenDomainMap.get(env)}/warden/searchLog/queryLog.json?indexId=tax-asynctask-preset&query=${encodeURIComponent(query)}&timeScale=*********&orderByField=@timestamp&orderByType=DESC&limit=1000&userName=gufl&startTime=${startTime}&endTime=${endTime}`);
        const proxyHost = '**************:3000'; // 可根据需要调整
        const wardenUrl = `http://${proxyHost}/proxy?url=${wardenTargetUrl}`;
        fetch(wardenUrl)
            .then(response => response.json())
            .then(data => {
                if (data.body && Array.isArray(data.body.logList)) {
                    renderData(data.body.logList);
                } else {
                    logTableBody.innerHTML = '<tr><td colspan="7">无数据</td></tr>';
                    hideLoading();
                }
            })
            .catch(error => {
                logTableBody.innerHTML = '<tr><td colspan="7">请求失败</td></tr>';
                hideLoading();
            });
    });
    </script>
</body>
</html>
