const express = require('express');
const axios = require('axios');
const qs = require('qs');

const app = express();
const PORT = process.env.PORT || 3000;

// 处理 CORS
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*'); // 允许所有来源
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS'); // 允许的 HTTP 方法
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization'); // 允许的请求头
    next();
});

// 获取当前时间，格式化为 yyyyMMddHHmmss
function getCurrentTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds() + 2).padStart(2, '0');
    return `${year}${month}${day}${hours}${minutes}${seconds}`;
}
let taxmanageDoaminMap = new Map();
taxmanageDoaminMap.set('tax-manage.dc.servyou-it.com', 'https://boss.dc.servyou-it.com');
taxmanageDoaminMap.set('pre-tax-manage.dc.servyou-it.com', 'https://pre-boss.dc.servyou-it.com');
taxmanageDoaminMap.set('tax-manage-pc.servyou-release.devops.91lyd.com', 'http://boss-manage-pc.servyou-release.devops.91lyd.com');
taxmanageDoaminMap.set('tax-manage-pc.dntax-test.sit.91lyd.com', 'http://boss-manage-pc.boss-test.sit.91lyd.com');

let taxmanageAccountMap = new Map();
taxmanageAccountMap.set('tax-manage.dc.servyou-it.com', 'Z3VmbA==');
taxmanageAccountMap.set('pre-tax-manage.dc.servyou-it.com', 'Z3VmbA==');
taxmanageAccountMap.set('tax-manage-pc.servyou-release.devops.91lyd.com', 'eHF5X2FkbWlu');
taxmanageAccountMap.set('tax-manage-pc.dntax-test.sit.91lyd.com', 'eHF5X2FkbWlu');

let taxmanagePwdMap = new Map();
taxmanagePwdMap.set('tax-manage.dc.servyou-it.com', 'WWhoQDIwMTkxMjA5');
taxmanagePwdMap.set('pre-tax-manage.dc.servyou-it.com', 'WWhoQDIwMTkxMjA5');
taxmanagePwdMap.set('tax-manage-pc.servyou-release.devops.91lyd.com', 'MTIz');
taxmanagePwdMap.set('tax-manage-pc.dntax-test.sit.91lyd.com', 'MTIz');
// 登录并获取 sessionCookie 和 ssoEpctoken
async function loginAndGetTokens(targetDoamin) {
    console.log(targetDoamin)
    const host = taxmanageDoaminMap.get(targetDoamin);
    const account = taxmanageAccountMap.get(targetDoamin);
    const pwd = taxmanagePwdMap.get(targetDoamin);

    const password = Buffer.from(Buffer.from(pwd, 'base64').toString('utf8') + getCurrentTime()).toString('base64');
    const data = qs.stringify({
        loginName: Buffer.from(account, 'base64').toString('utf8'),
        rememberMe: true,
        password: password
    });
    try {
        const loginUrl = host + '/xqy-portal-web/boss/login/base/login';
        console.log('loginUrl >>> ' + loginUrl);
        console.log('data >>> ' + data);
        const response = await axios.post(loginUrl, data, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });
        const sessionCookie = response.headers['set-cookie'];
        return sessionCookie; // 返回 sessionCookie
    } catch (error) {
        console.log('Login error:', error);
        throw error; // 抛出错误以便在调用时处理
    }

}

function isObject(value) {
    return value !== null && typeof value === 'object';
}

// 缓存 sessionCookie
let cachedSessionCookieMap = new Map();

// 定义代理路由
app.use('/proxy', async (req, res) => {
    const url = decodeURIComponent(req.query.url);

    const targetUrl = new URL(url);

    var cachedSessionCookie = cachedSessionCookieMap.get(targetUrl.hostname);
    const loginHost = taxmanageDoaminMap.get(targetUrl.hostname);
    if (loginHost) {
        if (!cachedSessionCookie) {
            console.log("无缓存，走登录")
            try {
                cachedSessionCookie = await loginAndGetTokens(targetUrl.hostname); // 获取并缓存 sessionCookie
                console.log('登录成功' + cachedSessionCookie);
                cachedSessionCookieMap.set(targetUrl.hostname, cachedSessionCookie);
            } catch (error) {
                return res.status(500).json({ message: 'Login failed', error: error.message });
            }
        } else {
            console.log("有缓存，不登录")
        }

    } else {
        console.log(targetUrl.hostname + '>>> host未配置，不登录');
    }

    console.log('登录处理完成')
    try {
        const response = await axios({
            method: req.method,
            url: url,
            data: req.body,
            headers: {
                'Content-Type': 'application/json',
                'Cookie': cachedSessionCookie
            }
        });
        console.log("是否对象" + isObject(response.data))
        // 如果返回 302，尝试重新登录并重试请求
        if (!isObject(response.data) && response.data.includes('运营平台单点登录')) {
            console.log("登录无效，需重新登陆");
            cachedSessionCookie = await loginAndGetTokens(); // 重新登录并更新缓存
            // 重试请求
            try {
                const retryResponse = await axios({
                    method: req.method,
                    url: url,
                    data: req.body,
                    headers: {
                        'Content-Type': 'application/json',
                        'Cookie': cachedSessionCookie
                    }
                });
                res.status(retryResponse.status).json(retryResponse.data);
            } catch (retryError) {
                res.status(retryError.response ? retryError.response.status : 500).json({
                    message: retryError.message,
                    error: retryError.response ? retryError.response.data : {}
                });
            }
        } else {
            console.log("目标接口请求成功")
            res.status(response.status).json(response.data);
        }

    } catch (error) {
        res.status(error.response ? error.response.status : 500).json({
            message: error.message,
            error: error.response ? error.response.data : {}
        });
    }
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`CORS Proxy Server is running on http://localhost:${PORT}`);
});