<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排队日志查询</title>
    <link href="./bootstrap.min.css" rel="stylesheet">
    <style>
        body {
        margin: 0;
        overflow: hidden; /* 隐藏滚动条 */
/*        background: #fafafa;
        color: white;*/
        height: 100vh;
    }
    .table-container {
        margin: 20px auto;
        width: 95%;
    }
    .custom-container {
        width: 95%;
        margin: 0 auto; /* 居中对齐 */
    }
    .trace-id {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
    }

    .tooltip-text {
        display: none;
    }

    .trace-id:hover .tooltip-text {
        display: block;
        position: absolute;
        background-color: rgba(0, 0, 0, 0.7);
        color: #fff;
        padding: 5px;
        border-radius: 5px;
        max-width: 400px;
        z-index: 10;
    }

    th.message-column,
    td.message-column {
        width: 50%;
        /* 设置具体的宽度 */
        word-wrap: break-word;
        /* 控制单词换行 */
        word-break: break-all;
        /* 控制超出部分换行 */
        white-space: normal;
        /* 允许文本换行 */
    }

    th.server-column,
    td.server-column {
        width: 10%;
        /* 设置具体的宽度 */
        word-wrap: break-word;
        /* 控制单词换行 */
        word-break: break-all;
        /* 控制超出部分换行 */
        white-space: normal;
        /* 允许文本换行 */
    }

    .modal-dialog {
        max-width: 60%;
    }




    .table-container {
        max-height: calc(100vh - 200px); /* 调整高度以适应屏幕底 */
        overflow-y: auto; /* 启用垂直滚动 */
        border: 1px solid #ddd;
    }

    /* 强制表格列宽根据设置的百分比来分配 */
    .table {

        width: 100%; /* 确保表格占满容器 */
    }

    thead th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa; /* 表头背景色 */
        z-index: 10;
        padding: 8px 16px;
        white-space: nowrap; /* 防止内容换行 */
    }

    .table th, .table td {
        text-align: left;
        padding: 8px 16px;
    }


    /* 针对 Webkit 浏览器（Chrome, Safari） */
    .table-container::-webkit-scrollbar {
      width: 10px; /* 默认滚动条宽度 */
      height: 10px; /* 默认水平滚动条高度 */
      transition: width 0.3s ease; /* 滚动条宽度变化时的过渡效果 */
    }

    .table-container::-webkit-scrollbar-thumb {
      background: #a0a0a0; /* 滚动条默认颜色（莫兰迪灰色） */
      border-radius: 6px;
      border: 2px solid #f2f2f2; /* 滚动条的边框 */
      transition: background 0.3s ease, width 0.3s ease; /* 滚动条背景和宽度变化时的过渡效果 */
    }

    .table-container::-webkit-scrollbar-thumb:hover {
      background: #6c757d; /* 鼠标悬停时，滚动条颜色加�� */
    }

    .table-container::-webkit-scrollbar:hover {
      width: 20px; /* 鼠标悬停时，滚动条加宽 */
      height: 20px; /* 鼠标悬停时，水平滚动条加高 */
    }

    .table-container::-webkit-scrollbar-track {
      background: #eaeaea; /* 滚动条轨道颜色（浅莫兰迪灰） */
      border-radius: 6px;
      transition: background 0.3s ease; /* 滚动条轨道背景变化的过渡效果 */
    }


    .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .spinner {
            width: 80px;
            height: 80px;
            border: 8px solid rgba(255, 255, 255, 0.3);
            border-top-color: white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }


        .snowflake {
            position: absolute;
            top: -10px;
            font-size: 1em;
            color: black;
            opacity: 0.9;
            animation: fall linear infinite;
/*            animation: fall linear infinite, rotate linear infinite;*/

            z-index: 11;
        }

        @keyframes fall {
            0% {
                transform: translateY(0);
            }
            100% {
                transform: translateY(100vh);
            }
        }
/*
        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }*/
    </style>
</head>

<body>
    <div class="custom-container container-fluid mt-4">
        <h2>排队日志查询</h2>
        <div class="row mb-3">
            <div class="col" style="display: none" id="proxyConfigDiv">
                <label for="proxyConfig" class="form-label">代理IP</label>
                <input type="text" class="form-control" id="proxyConfig" value="**************:3000">
            </div>
            <!--             <div class="col">
                <label for="env" class="form-label">环境</label>
                <input type="text" class="form-control" id="env" list="envOptions" value="prod">
                <datalist id="envOptions">
                    <option value="prod">
                    <option value="pre">
                    <option value="release">
                    <option value="test">
                </datalist>
            </div> -->
            <div class="col">
                <label for="env" class="form-label">环境</label>
                <div class="dropdown">
                    <button class="btn btn-secondary dropdown-toggle" type="button" id="envDropdown" data-bs-toggle="dropdown" aria-expanded="false" style="width: 100%;background-color: white;color: black;">
                        prod
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="envDropdown" style="width: 100%;text-align: center;min-width: 100%; ">
                        <li><a class="dropdown-item" href="#" onclick="selectEnv('prod')">prod</a></li>
                        <li><a class="dropdown-item" href="#" onclick="selectEnv('pre')">pre</a></li>
                        <li><a class="dropdown-item" href="#" onclick="selectEnv('release')">release</a></li>
                        <li><a class="dropdown-item" href="#" onclick="selectEnv('test')">test</a></li>
                    </ul>
                </div>
            </div>
            <div class="col">
                <label for="traceId" class="form-label">TraceId</label>
                <input type="text" class="form-control" id="traceId">
            </div>
            <div class="col">
                <label for="taskId" class="form-label">TaskId</label>
                <input type="text" class="form-control" id="taskId">
            </div>
            <div class="col">
                <label for="startTime" class="form-label">时间起</label>
                <input type="datetime-local" class="form-control" id="startTime">
            </div>
            <div class="col">
                <label for="endTime" class="form-label">时间止</label>
                <input type="datetime-local" class="form-control" id="endTime">
            </div>
            <div class="col">
                <label for="keyword" class="form-label">关键词</label>
                <input type="text" class="form-control" id="keyword" list="keywordOptions">
                <datalist id="keywordOptions">
                    <option value="凭证校验checkTicket">
                    <option value="获取凭证开始">
                    <option value="任务落库成功">
                        <!-- <option value="获得优先级任务"> -->
                    <option value="生成凭证">
                    <option value="发送业务通知MQ[业务]">
                    <option value="调用方">
                </datalist>
            </div>
            <div class="col">
                <label for="operate" class="form-label">操作</label>
                <div class="col-auto align-self-end"><button id="queryButton" class="btn btn-primary">查询</button></div>
            </div>
        </div>
        <div class="row mb-3">
        </div>
        <div class="mb-3">
        </div>
    </div>
    <div class="table-container">
        <table class="table table-hover">
            <thead class="thead-light">
                <tr>
                    <th scope="col" style="width: 10%;">time</th>
                    <th scope="col" style="width: 5%">taskId</th>
                    <th scope="col" style="width: 5%">traceId</th>
                    <th scope="col" style="width: 55%">event</th>
                    <th scope="col" style="width: 10%">sourceHost</th>
                    <th scope="col" style="width: 10%">clusterName</th>
                </tr>
            </thead>
            <tbody id="log-data">
                <!-- 数据行将由JavaScript渲染 -->
            </tbody>
        </table>
    </div>
    <!-- Bootstrap 模态框 -->
    <div class="modal fade" id="responseModal" tabindex="-1" aria-labelledby="responseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="responseModalLabel">详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <pre id="modalContent"></pre>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 全屏遮罩 -->
    <!--     <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-text">加载中...</div>
    </div> -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="spinner"></div>
    </div>
    <script src="./bootstrap.bundle.min.js"></script>
    <script>
    // 创建雪花的函数
    function createSnowflake() {
        const snowflake = document.createElement('div');
        snowflake.classList.add('snowflake');
        snowflake.textContent = '无bug'; // 雪花符号
        // snowflake.textContent = '❄'; // 雪花符号

        // 随机设置雪花的初始位置和大小
        snowflake.style.left = Math.random() * 100 + 'vw';
        snowflake.style.animationDuration = Math.random() * 9 + 4 + 's'; // 设置不同速度
        snowflake.style.fontSize = Math.random() * 10 + 10 + 'px'; // 设置不同大小

        document.body.appendChild(snowflake);

        // 雪花下落完后移除
        setTimeout(() => {
            snowflake.remove();
        }, 5000);
    }



    function selectEnv(env) {
        document.getElementById('envDropdown').textContent = env;
    }
    let wardenDomainMap = new Map();
    wardenDomainMap.set('prod', 'https://warden.dc.servyou-it.com');
    wardenDomainMap.set('pre', 'https://warden.dc.servyou-it.com');
    wardenDomainMap.set('release', 'http://warden-eyes-front.servyou-stable.sit.91lyd.com');
    wardenDomainMap.set('test', 'http://warden-eyes-front.servyou-stable.sit.91lyd.com');
    let taxmanageMap = new Map();
    taxmanageMap.set('prod', 'http://tax-manage.dc.servyou-it.com');
    taxmanageMap.set('pre', 'http://pre-tax-manage.dc.servyou-it.com');
    taxmanageMap.set('release', 'http://tax-manage-pc.servyou-release.devops.91lyd.com');
    taxmanageMap.set('test', 'http://tax-manage-pc.dntax-test.sit.91lyd.com');
    let messageUrl = new Map();
    messageUrl.set('test', 'http://governor-ui.servyou-stable.sit.91lyd.com/rocketMQ/messageManager?topic=test%taxlineup-topic-biz-notify&key=replaceKey&type=1');
    messageUrl.set('release', 'http://governor-ui.servyou-stable.sit.91lyd.com/rocketMQ/messageManager?topic=release%taxlineup-topic-biz-notify&key=replaceKey&type=1');
    messageUrl.set('pre', 'http://governor.hz.servyou-it.com/rocketMQ/messageManager?topic=pre%taxlineup-topic-biz-notify&key=replaceKey&type=1');
    messageUrl.set('prod', 'http://governor.hz.servyou-it.com/rocketMQ/messageManager?topic=prod%taxlineup-topic-biz-notify&key=replaceKey&type=1');

    const logTableBody = document.getElementById('log-data');
    var currentDisplay = false;

    // 目标序列: 上 下 上 下 左 右 左 右 A B A B
    const targetSequence = ['ArrowUp', 'ArrowDown', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyA', 'KeyB', 'KeyA', 'KeyB'];
    let currentSequence = [];
    const targetSequence2 = ['ArrowDown', 'ArrowDown', 'ArrowDown'];
    let currentSequence2 = [];

    // 启动雪花生成
    function startSnow() {
        snowflakeInterval = setInterval(createSnowflake, 100); // 每隔2000毫秒创建一片雪花
    }

    // 停止雪花生成
    function stopSnow() {
        clearInterval(snowflakeInterval); // 清除定时器
    }
    var snowFlag = false;

    function switchSnow() {
        if (snowFlag) {
            snowFlag = false;
            stopSnow();
        } else {
            snowFlag = true;
            startSnow();
        }
    }
    // 监听按键事件
    document.addEventListener('keydown', function(event) {
        const key = event.code;

        // 将按键加入到当前序列
        currentSequence.push(key);
        currentSequence2.push(key);

        // 检查当前序列是否超出目标长度
        if (currentSequence.length > targetSequence.length) {
            currentSequence.shift(); // 超过长度时删除最早的按键
        }
        if (currentSequence2.length > targetSequence2.length) {
            currentSequence2.shift(); // 超过长度时删除最早的按键
        }

        // 比较当前序列和目标序列
        if (currentSequence.join('') === targetSequence.join('')) {
            document.getElementById('proxyConfigDiv').style.display = currentDisplay ? 'none' : 'block';
            currentDisplay = !currentDisplay;
            currentSequence = []; // 匹配成功后重置序列
        }

        if (currentSequence2.join('') === targetSequence2.join('')) {
            switchSnow();
            currentSequence2 = []; // 匹配成功后重置序列
        }
    });

    function formatTimestamp(timestamp, gap, needMillis) {
        const date = new Date(timestamp + gap * 60 * 1000);

        // 获取各个部分
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        const milliseconds = String(date.getMilliseconds()).padStart(3, '0');
        if (needMillis) {
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
        } else {
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }

    }

    function formatyyyyMMddHHmmss(timestamp) {
        const date = new Date(timestamp);

        // 获取各个部分
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        const milliseconds = String(date.getMilliseconds()).padStart(3, '0');
        return `${year}${month}${day}${hours}${minutes}${seconds}`;

    }

    function showLoading() {
        document.getElementById("loadingOverlay").style.display = "flex"; // 显示遮罩
        switchSnow();
    }

    function hideLoading() {
        document.getElementById("loadingOverlay").style.display = "none"; // 隐藏遮罩
        switchSnow();
    }

    function getMessageUrl(taskId) {
        const env = document.getElementById('envDropdown').textContent.trim();
        var tempmessageUrl = messageUrl.get(env);
        return tempmessageUrl.replace('replaceKey', taskId);
    }

    function renderData(data) {
        logTableBody.innerHTML = '';
        data.forEach(log => {
            const row = document.createElement('tr');
            var msgStyele = '';

            if (log.fields.msg.includes('凭证校验checkTicket')) {
                msgStyele = 'style="background-color: #d0f0c0;"';
            } else if (log.fields.msg.includes('获取凭证开始')) {
                msgStyele = 'style="background-color: #b3e5fc;"';
            } else if (log.fields.msg.includes('任务落库成功')) {
                msgStyele = 'style="background-color: #ffd180;"';
            }
            // else if (log.fields.msg.includes('获得优先级任务')) {
            //     msgStyele = 'style="background-color: #ffeb3b;"';
            // } 
            else if (log.fields.msg.includes('生成凭证')) {
                msgStyele = 'style="background-color: #e1bee7;"';
            } else if (log.fields.msg.includes('发送业务通知MQ[业务]')) {
                msgStyele = 'style="background-color: #ff8a80;"';
            } else if (log.fields.msg.includes('调用方')) {
                msgStyele = 'style="background-color: #e0e0e0;"';
            } else {
                msgStyele = 'style="background-color: #e0e7ea;"';
            }
            var msgUrl = getMessageUrl(log.fields.taskId);

            row.innerHTML = `
                <td class="server-column">${formatTimestamp(log.timestamp,0,true)}</td>

                <td class="server-column">${log.fields.taskId === '>>>>>>' || log.fields.taskId === '000000' ? log.fields.taskId:`<a href='#' class='ticket-link' data-taskId='${log.fields.taskId}' data-time='${log.timestamp}'>${log.fields.taskId}</a>`}</td>

                <td class="server-column">${log.fields.traceId}</td>
                <td class="message-column" ${msgStyele}>
                ${log.fields.msg ? log.fields.msg.replace(/("ticket":"(.*?)"|ticket=([a-zA-Z0-9\-_=]+))/g, (match, p1, p2, p3) => {
                    const ticketValue = p2 || p3; // 如果有 p2 则使用 p2，否则使用 p3
                    return `"ticket":"<a href='#' class='ticket-link' data-ticket='${ticketValue}' data-time='${log.timestamp}'>${ticketValue}</a>"`;}).replace(/code=000000/g, '<a href='+msgUrl+' target="_blank">code=000000[可执行通知]</a>').replace(/code=000001/g, '<a href='+msgUrl+' target="_blank">code=000001[执行超时]</a>').replace(/code=000002/g, '<a href='+msgUrl+' target="_blank">code=000002[排队超时]</a>').replace(/code=000003/g, 'code=000003[主动取消]').replace(/code=000004/g, '<a href='+msgUrl+' target="_blank">code=000004[强制被中断]</a>') : ""}
                </td>

                <td class="server-column"><a href='http://phoenix.dc.servyou-it.com/v2/#/ssh/terminal?appCode=taxlineup&podName=${log.fields['@sourceHost']}' target="_blank">${log.fields['@sourceHost']}</a></td>
                <td class="server-column">${log.fields['@clusterName']}</td>
            `;
            logTableBody.appendChild(row);
        });

        document.querySelectorAll('.ticket-link').forEach(link => {
            link.addEventListener('click', function(event) {
                event.preventDefault();
                const ticketValue = this.getAttribute('data-ticket');
                const taskId = this.getAttribute('data-taskId');
                const createTime = Number(this.getAttribute('data-time'));
                const env = document.getElementById('envDropdown').textContent.trim();
                let targetUrl = '';
                if (taskId) {
                    targetUrl = encodeURIComponent(`${taxmanageMap.get(env)}/taxmanage/taxlineup/task/query?startCreateTime=${formatTimestamp(createTime, -60, false)}&endCreateTime=${formatTimestamp(createTime, 30, false)}&taskId=${taskId}&currentPage=1&size=9999`);
                } else {
                    targetUrl = encodeURIComponent(`${taxmanageMap.get(env)}/taxmanage/taxlineup/task/query?startCreateTime=${formatTimestamp(createTime, -60, false)}&endCreateTime=${formatTimestamp(createTime, 30, false)}&ticket=${ticketValue}&currentPage=1&size=9999`);
                }
                const proxyHost = document.getElementById('proxyConfig').value.trim();

                const url = `http://${proxyHost}/proxy?url=` + targetUrl;

                // 发送GET请求
                fetch(url)
                    .then(response => response.json()) // 假设返回的是JSON格式
                    .then(data => {
                        // 把返回的内容放入模态框中
                        document.getElementById('modalContent').innerText = JSON.stringify(data.body.result[0], null, 2);

                        // 显示模态框
                        const modal = new bootstrap.Modal(document.getElementById('responseModal'));
                        modal.show();
                    })
                    .catch(error => {
                        console.error('请求出错:', error);
                        document.getElementById('modalContent').innerText = '请求失败，请稍后重试。';
                        const modal = new bootstrap.Modal(document.getElementById('responseModal'));
                        modal.show();
                    });
            });
        });
        hideLoading();
    }
    // 默认开始和结束时间
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
    const fiveMinutesLater = new Date(now.getTime() + 5 * 60 * 1000);
    const options = { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', hour12: false };

    document.getElementById('startTime').value = fiveMinutesAgo.toLocaleString('sv-SE', options).replace(' ', 'T');
    document.getElementById('endTime').value = fiveMinutesLater.toLocaleString('sv-SE', options).replace(' ', 'T');

    document.getElementById('queryButton').addEventListener('click', () => {
        logTableBody.innerHTML = '';
        showLoading();
        const traceId = document.getElementById('traceId').value.trim();
        const taskId = document.getElementById('taskId').value.trim();
        const startTime = formatyyyyMMddHHmmss(new Date(document.getElementById('startTime').value));
        const endTime = formatyyyyMMddHHmmss(new Date(document.getElementById('endTime').value + ':59.999'));
        const keyword = document.getElementById('keyword').value.trim();
        const env = document.getElementById('envDropdown').textContent.trim();

        let query = '@source:"/usr/local/logs/taxlineup/common-schedule.log" AND @sourceHost:*' + env + '*';

        if (taskId && taskId.trim()) {
            query += ' AND ';
            query += `taskId:"${taskId}"`;
        }

        if (traceId && traceId.trim()) {
            query += ' AND ';
            query += `traceId:"${traceId}"`;
        }

        if (keyword && keyword.trim()) {
            query += ' AND ';
            query += `msg:("${keyword}")`;
        }


        const wardenTargetUrl = encodeURIComponent(`${wardenDomainMap.get(env)}/warden/searchLog/queryLog.json?indexId=taxlineup-preset&query=${encodeURIComponent(query)}&timeScale=*********&orderByField=@timestamp&orderByType=DESC&limit=1000&userName=gufl&startTime=${startTime}&endTime=${endTime}`);

        const proxyHost = document.getElementById('proxyConfig').value.trim();

        const wardenUrl = `http://${proxyHost}/proxy?url=${wardenTargetUrl}`;
        console.log(wardenUrl);
        fetch(wardenUrl)
            .then(response => response.json())
            .then(data => {
                var firstData = data.body.logList;
                console.log(firstData.length);
                // 处理返回数据并渲染到表���
                if (!keyword && taskId && taskId.trim() !== '' && Array.isArray(firstData) && firstData.length > 0) {
                    const filteredLogs = data.body.logList.filter(log => log.fields.msg.includes('任务落库成功'));
                    if (filteredLogs.length > 0) {
                        const traceId2 = filteredLogs[0].fields.traceId;
                        let query2 = '@source:"/usr/local/logs/taxlineup/common-schedule.log"';

                        if (traceId2 && traceId2.trim()) {
                            query2 += ' AND ';
                            query2 += `traceId:"${traceId2}"`;
                        }

                        const wardenTargetUrl2 = encodeURIComponent(`${wardenDomainMap.get(env)}/warden/searchLog/queryLog.json?indexId=taxlineup-preset&query=${encodeURIComponent(query2)}&timeScale=*********&orderByField=@timestamp&orderByType=DESC&limit=1000&userName=gufl&startTime=${startTime}&endTime=${endTime}`);
                        const wardenUrl2 = `http://${proxyHost}/proxy?url=${wardenTargetUrl2}`;

                        console.log(wardenUrl2);
                        fetch(wardenUrl2)
                            .then(response2 => response2.json())
                            .then(data2 => {
                                console.log(data2.body.logList.length);
                                const mergedArray = firstData.concat(data2.body.logList);
                                const totalData = Array.from(new Map(mergedArray.map(item => [item._id, item])).values());
                                totalData.sort((a, b) => b.timestamp - a.timestamp);

                                console.log(totalData.length);
                                renderData(totalData);
                            })
                            .catch(error => {
                                console.error('请求出错:', error);
                                hideLoading();
                                // 显示错误信息
                            });
                    } else {
                        renderData(data.body.logList);
                    }
                } else {
                    renderData(data.body.logList);
                }
            })
            .catch(error => {
                console.error('请求出错:', error);
                hideLoading();
                // 显示错误信息
            });
    });
    </script>
</body>

</html>