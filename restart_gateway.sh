#!/bin/bash

# 定义日志文件路径和主服务文件路径
LOG_FILE="output.log"
SERVER_SCRIPT="gwProxyType.js"

# 查找并杀死正在运行的 mainServer.js 进程
echo "Stopping the server..."
PIDS=$(pgrep -f "$SERVER_SCRIPT")

if [ -z "$PIDS" ]; then
  echo "No running process found for $SERVER_SCRIPT."
else
  # 终止所有匹配的进程
  kill $PIDS
  echo "Killed processes: $PIDS"
fi

# 等待一段时间以确保进程完全停止
sleep 2

# 检查是否还有未终止的进程
REMAINING_PIDS=$(pgrep -f "$SERVER_SCRIPT")
if [ ! -z "$REMAINING_PIDS" ]; then
  echo "Some processes are still running. Force killing them..."
  kill -9 $REMAINING_PIDS
fi

# 清理旧日志（可选）
if [ -f "$LOG_FILE" ]; then
  echo "Cleaning up old log file..."
  > "$LOG_FILE" # 清空日志文件内容
fi

# 启动新的服务实例
echo "Starting the server..."
nohup node "$SERVER_SCRIPT" > "$LOG_FILE" 2>&1 &

# 获取最新启动的匹配进程 PID（更可靠的方式）
sleep 1
NEW_PID=$(pgrep -f "$SERVER_SCRIPT" | grep -v "grep" | head -n 1)

echo "Server restarted successfully. Logs are being written to $LOG_FILE."
echo "New process ID: $NEW_PID"
