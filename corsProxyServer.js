const express = require('express');
const axios = require('axios');
const qs = require('qs');
const multer = require('multer');

const app = express();
const PORT = process.env.PORT || 3000;

// 配置multer用于处理multipart/form-data
const upload = multer();

// 中间件配置 - 支持多种Content-Type
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(upload.any()); // 处理multipart/form-data

// 处理 CORS
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*'); // 允许所有来源
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS'); // 允许的 HTTP 方法
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization'); // 允许的请求头
    next();
});

// 获取当前时间，格式化为 yyyyMMddHHmmss
function getCurrentTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds() + 2).padStart(2, '0');
    return `${year}${month}${day}${hours}${minutes}${seconds}`;
}
let taxmanageDoaminMap = new Map();
taxmanageDoaminMap.set('tax-manage.dc.servyou-it.com', 'https://boss.dc.servyou-it.com');
taxmanageDoaminMap.set('pre-tax-manage.dc.servyou-it.com', 'https://pre-boss.dc.servyou-it.com');
taxmanageDoaminMap.set('tax-manage-pc.servyou-release.devops.91lyd.com', 'http://boss-manage-pc.servyou-release.devops.91lyd.com');
taxmanageDoaminMap.set('tax-manage-pc.dntax-test.sit.91lyd.com', 'http://boss-manage-pc.boss-test.sit.91lyd.com');
taxmanageDoaminMap.set('phoenix.dc.servyou-it.com', 'http://phoenix.dc.servyou-it.com');
taxmanageDoaminMap.set('warden.dc.servyou-it.com', 'https://warden.dc.servyou-it.com')

let pathMap = new Map();
pathMap.set('tax-manage.dc.servyou-it.com', '/xqy-portal-web/boss/login/base/login');
pathMap.set('pre-tax-manage.dc.servyou-it.com', '/xqy-portal-web/boss/login/base/login');
pathMap.set('tax-manage-pc.servyou-release.devops.91lyd.com', '/xqy-portal-web/boss/login/base/login');
pathMap.set('tax-manage-pc.dntax-test.sit.91lyd.com', '/xqy-portal-web/boss/login/base/login');
pathMap.set('phoenix.dc.servyou-it.com', '/phoenix/ssologin/submitLogin');
pathMap.set('warden.dc.servyou-it.com', '/warden/login/login.json');

let taxmanageAccountMap = new Map();
taxmanageAccountMap.set('tax-manage.dc.servyou-it.com', 'Z3VmbA==');
taxmanageAccountMap.set('pre-tax-manage.dc.servyou-it.com', 'Z3VmbA==');
taxmanageAccountMap.set('tax-manage-pc.servyou-release.devops.91lyd.com', 'eHF5X2FkbWlu');
taxmanageAccountMap.set('tax-manage-pc.dntax-test.sit.91lyd.com', 'eHF5X2FkbWlu');
taxmanageAccountMap.set('phoenix.dc.servyou-it.com', 'Z3VmbA==');
taxmanageAccountMap.set('warden.dc.servyou-it.com', 'Z3VmbA==');

let taxmanagePwdMap = new Map();
taxmanagePwdMap.set('tax-manage.dc.servyou-it.com', 'WWhoQDIwMTkxMjA5');
taxmanagePwdMap.set('pre-tax-manage.dc.servyou-it.com', 'WWhoQDIwMTkxMjA5');
taxmanagePwdMap.set('tax-manage-pc.servyou-release.devops.91lyd.com', 'MTIz');
taxmanagePwdMap.set('tax-manage-pc.dntax-test.sit.91lyd.com', 'MTIz');
taxmanagePwdMap.set('phoenix.dc.servyou-it.com', 'WWhoQDIwMTkxMjA5');
taxmanagePwdMap.set('warden.dc.servyou-it.com', 'WWhoQDIwMTkxMjA5');

// 登录并获取 sessionCookie 和 ssoEpctoken
async function loginAndGetTokens(targetDoamin) {
    console.log("targetDoamin:" + targetDoamin)
    const host = taxmanageDoaminMap.get(targetDoamin);
    const path = pathMap.get(targetDoamin);
    const account = taxmanageAccountMap.get(targetDoamin);
    const pwd = taxmanagePwdMap.get(targetDoamin);

    const password = Buffer.from(Buffer.from(pwd, 'base64').toString('utf8') + getCurrentTime()).toString('base64');
    var data = 'application/json;charset=UTF-8';
    var contentType = 'application/x-www-form-urlencoded';
    if ('warden.dc.servyou-it.com' === targetDoamin){
        // contentType = 'application/json; charset=utf-8'
        data = qs.stringify({
            username:Buffer.from(account, 'base64').toString('utf8'),
            password: 'z4XWOgAH6i7hybxvmQXDqw=='
        });
    } else if ('phoenix.dc.servyou-it.com' === targetDoamin){
        data = qs.stringify({
            loginUsername: Buffer.from(account, 'base64').toString('utf8'),
            loginPassword: password
        });
    } else {
        data = qs.stringify({
            loginName: Buffer.from(account, 'base64').toString('utf8'),
            rememberMe: true,
            password: password
        });
    }
    
    try {
        const loginUrl = host + path;
        console.log('loginUrl >>> ' + loginUrl);
        console.log('data >>> ' + data);
        const response = await axios.post(loginUrl, data, {
            headers: {
                'Content-Type': contentType
            }
        });
        const sessionCookie = response.headers['set-cookie'];
        return sessionCookie; // 返回 sessionCookie
    } catch (error) {
        console.log('Login error:', error);
        throw error; // 抛出错误以便在调用时处理
    }

}

function isObject(value) {
    return value !== null && typeof value === 'object';
}

// 处理请求头和请求体的函数
function prepareRequestData(req) {
    const contentType = req.get('Content-Type') || 'application/json';
    let requestData = req.body;
    let headers = {
        'Cookie': '' // 这里会在调用时设置
    };

    console.log('Original Content-Type:', contentType);
    console.log('Request body:', req.body);
    console.log('Request files:', req.files);

    // 处理不同的Content-Type
    if (contentType.includes('multipart/form-data')) {
        // 处理multipart/form-data (文件上传)
        const FormData = require('form-data');
        const formData = new FormData();

        // 添加普通字段
        if (req.body) {
            Object.keys(req.body).forEach(key => {
                formData.append(key, req.body[key]);
            });
        }

        // 添加文件字段
        if (req.files && req.files.length > 0) {
            req.files.forEach(file => {
                formData.append(file.fieldname, file.buffer, {
                    filename: file.originalname,
                    contentType: file.mimetype
                });
            });
        }

        requestData = formData;
        headers = {
            ...formData.getHeaders(),
            'Cookie': '' // 这里会在调用时设置
        };

        console.log('Prepared FormData headers:', headers);
    } else if (contentType.includes('application/x-www-form-urlencoded')) {
        // 处理form-urlencoded
        headers['Content-Type'] = 'application/x-www-form-urlencoded';
        // requestData 保持原样，express已经解析了
    } else {
        // 默认处理为JSON (保持向后兼容)
        headers['Content-Type'] = 'application/json';
        // requestData 保持原样
    }

    return { requestData, headers };
}

// 缓存 sessionCookie
let cachedSessionCookieMap = new Map();

// 定义代理路由
app.use('/proxy', async (req, res) => {
    const url = req.query.url;

    const targetUrl = new URL(url);

    var cachedSessionCookie = cachedSessionCookieMap.get(targetUrl.hostname);
    console.log("targetUrl.hostname:" + targetUrl.hostname);
    const loginHost = taxmanageDoaminMap.get(targetUrl.hostname);
    if (loginHost) {
        if (!cachedSessionCookie) {
            console.log("无缓存，走登录")
            try {
                cachedSessionCookie = await loginAndGetTokens(targetUrl.hostname); // 获取并缓存 sessionCookie
                console.log('登录成功' + cachedSessionCookie);
                cachedSessionCookieMap.set(targetUrl.hostname, cachedSessionCookie);
            } catch (error) {
                return res.status(500).json({ message: 'Login failed', error: error.message });
            }
        } else {
            console.log("有缓存，不登录")
        }

    } else {
        console.log(targetUrl.hostname + '>>> host未配置，不登录');
    }

    console.log('登录处理完成')
    try {
        // 准备请求数据和头部
        const { requestData, headers } = prepareRequestData(req);
        headers['Cookie'] = cachedSessionCookie;

        console.log("Prepared request data:", requestData);
        console.log("Prepared headers:", headers);

        const response = await axios({
            method: req.method,
            url: url,
            data: requestData,
            headers: headers
        });
        console.log("response.data:" + response.data)
        console.log("是否对象" + isObject(response.data))
        // 如果返回 302，尝试重新登录并重试请求
        if (!isObject(response.data) && response.data.includes('运营平台单点登录')) {
            console.log("登录无效，需重新登陆");
            cachedSessionCookie = await loginAndGetTokens(targetUrl.hostname); // 重新登录并更新缓存
            cachedSessionCookieMap.set(targetUrl.hostname, cachedSessionCookie); // 更新缓存

            // 重试请求 - 使用相同的请求数据和头部
            try {
                const { requestData: retryRequestData, headers: retryHeaders } = prepareRequestData(req);
                retryHeaders['Cookie'] = cachedSessionCookie;

                const retryResponse = await axios({
                    method: req.method,
                    url: url,
                    data: retryRequestData,
                    headers: retryHeaders
                });
                res.status(retryResponse.status).json(retryResponse.data);
            } catch (retryError) {
                res.status(retryError.response ? retryError.response.status : 500).json({
                    message: retryError.message,
                    error: retryError.response ? retryError.response.data : {}
                });
            }
        } else {
            console.log("目标接口请求成功")
            res.status(response.status).json(response.data);
        }

    } catch (error) {
        res.status(error.response ? error.response.status : 500).json({
            message: error.message,
            error: error.response ? error.response.data : {}
        });
    }
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`CORS Proxy Server is running on http://localhost:${PORT}`);
});