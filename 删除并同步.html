<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>删除并同步</title>
    <link href="./bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 10px;
            background-color: #f8f9fa;
        }

        /* 在大屏幕上增加一些边距 */
        @media (min-width: 1400px) {
            body {
                padding: 15px;
            }
        }

        /* 在小屏幕上减少边距 */
        @media (max-width: 768px) {
            body {
                padding: 5px;
            }
        }
        .main-container {
            width: 100%;
            min-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }

        /* 在小屏幕上调整内边距 */
        @media (max-width: 768px) {
            .main-container {
                padding: 15px;
                border-radius: 0;
            }
        }
        .upload-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            border: 2px dashed #dee2e6;
        }

        /* 紧凑布局优化 */
        .upload-section .form-control {
            height: 38px;
        }

        .upload-section .btn {
            height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .upload-section .environment-indicator {
            height: 38px;
            display: flex;
            align-items: center;
            margin: 0;
        }
        .table-container {
            max-height: 60vh;
            min-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            transition: opacity 0.3s ease;
        }

        .table-container.refreshing {
            opacity: 0.7;
        }

        /* 在小屏幕上调整表格高度 */
        @media (max-width: 768px) {
            .table-container {
                max-height: 50vh;
                min-height: 300px;
            }

            /* 小屏幕上上传区域改为垂直布局 */
            .upload-section .row.align-items-end > div {
                margin-bottom: 15px;
            }

            .upload-section .row.align-items-end > div:last-child {
                margin-bottom: 0;
            }
        }
        .table {
            margin-bottom: 0;
            table-layout: fixed;
            width: 100%;
        }

        /* 表格单元格文本处理 */
        .table td, .table th {
            word-wrap: break-word;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 响应式表格调整 */
        @media (max-width: 1200px) {
            .table-container {
                font-size: 0.9em;
            }
        }

        @media (max-width: 768px) {
            .table-container {
                font-size: 0.8em;
            }

            .table td, .table th {
                padding: 8px 4px;
            }
        }
        .table thead th {
            position: sticky;
            top: 0;
            background-color: #495057;
            color: white;
            border: none;
            padding: 12px;
            font-weight: 600;
        }
        .table tbody td {
            padding: 12px;
            vertical-align: middle;
            border-bottom: 1px solid #dee2e6;
        }
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-processing {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
        }
        .btn-download {
            padding: 4px 12px;
            font-size: 12px;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 9999;
            display: none;
            justify-content: center;
            align-items: center;
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top-color: white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .file-input-wrapper {
            position: relative;
            display: inline-block;
        }
        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        .upload-info {
            margin-top: 10px;
            font-size: 14px;
            color: #6c757d;
        }
        .alert {
            margin-bottom: 20px;
        }
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        .text-danger {
            color: #dc3545 !important;
        }
        .form-text {
            font-size: 0.875em;
            color: #6c757d;
        }
        .form-select:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        .alert-info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .environment-indicator {
            border-left: 4px solid #0d6efd;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 6px;
            transition: all 0.3s ease;
        }
        .environment-indicator:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .production-warning {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
            100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
        }
    </style>
</head>
<body>
    <div class="main-container">

        <!-- 环境选择区域 -->
        <div class="mb-4">
            <div class="row">
                <div class="col-md-4">
                    <label for="environmentSelect" class="form-label">环境选择</label>
                    <select id="environmentSelect" class="form-select">
                        <option value="test">测试环境</option>
                        <option value="release" selected>Release环境</option>
                        <option value="production" id="productionOption" style="display: none;">生产环境</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 上传区域 -->
        <div class="upload-section">

            <!-- 所有输入元素在同一行 -->
            <div class="row align-items-end">
                <div class="col-md-2">
                    <label for="taxCodeInput" class="form-label">taxCode <span class="text-danger">*</span></label>
                    <input type="text" id="taxCodeInput" class="form-control" placeholder="" required>
                </div>
                <div class="col-md-2">
                    <label for="periodInput" class="form-label">period <span class="text-danger">*</span></label>
                    <input type="text" id="periodInput" class="form-control" placeholder="" required>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Excel文件 <span class="text-danger">*</span></label>
                    <div class="file-input-wrapper">
                        <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls" />
                        <button class="btn btn-outline-primary w-100">
                            <i class="bi bi-cloud-upload"></i> 选择Excel文件
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="alert alert-info environment-indicator mb-0" style="padding: 6px 8px; font-size: 0.8em;">
                        <i class="bi bi-server"></i> <strong>环境：</strong><span id="currentEnvironment">Release</span>
                    </div>
                </div>
                <div class="col-md-2">
                    <button id="uploadBtn" class="btn btn-success w-100" disabled>
                        <i class="bi bi-upload"></i> 上传
                    </button>
                </div>
            </div>

            <!-- 提示信息行 -->
            <div class="row mt-2">
                <div class="col-md-2">
                    <small class="text-muted">请输入有效的税务代码</small>
                </div>
                <div class="col-md-2">
                    <small class="text-muted">格式如：202401</small>
                </div>
                <div class="col-md-4">
                    <small class="text-muted">支持格式：.xlsx, .xls，文件大小不超过10MB</small>
                    <div id="selectedFileName" class="mt-1 text-primary fw-bold" style="font-size: 0.85em;"></div>
                </div>
                <div class="col-md-4"></div>
            </div>
        </div>

        <!-- 消息提示区域 -->
        <div id="messageArea"></div>

        <!-- 任务列表 -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">任务列表</h5>
            <button id="refreshBtn" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-clockwise"></i> 刷新
            </button>
        </div>

        <div class="table-container">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th style="width: 4%;">ID</th>
                        <th style="width: 20%;">文件名</th>
                        <th style="width: 12%;">taxCode</th>
                        <th style="width: 8%;">period</th>
                        <th style="width: 6%;">总数</th>
                        <th style="width: 6%;">成功</th>
                        <th style="width: 6%;">失败</th>
                        <th style="width: 8%;">状态</th>
                        <th style="width: 12%;">创建者</th>
                        <th style="width: 12%;">创建时间</th>
                        <th style="width: 6%;">操作</th>
                    </tr>
                </thead>
                <tbody id="taskTableBody">
                    <tr>
                        <td colspan="11" class="text-center text-muted">暂无数据</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="spinner"></div>
    </div>

    <!-- 口令验证弹窗 -->
    <div class="modal fade" id="secretCodeModal" tabindex="-1" aria-labelledby="secretCodeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="secretCodeModalLabel">
                        <i class="bi bi-shield-lock"></i> 生产环境访问验证
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning d-flex align-items-center" role="alert">
                        <i class="bi bi-exclamation-triangle flex-shrink-0 me-2"></i>
                        <div>
                            <strong>安全验证</strong> - 请输入生产环境访问口令
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="secretCodeInput" class="form-label">访问口令</label>
                        <input type="password" class="form-control" id="secretCodeInput" placeholder="请输入口令">
                        <div class="form-text">请联系管理员获取生产环境访问口令</div>
                    </div>
                    <div id="secretCodeError" class="alert alert-danger d-none">
                        <i class="bi bi-x-circle"></i> 口令错误，请重新输入
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle"></i> 取消
                    </button>
                    <button type="button" class="btn btn-warning" id="verifySecretCodeBtn">
                        <i class="bi bi-check-circle"></i> 验证口令
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 生产环境确认弹窗 -->
    <div class="modal fade" id="productionConfirmModal" tabindex="-1" aria-labelledby="productionConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="productionConfirmModalLabel">
                        <i class="bi bi-exclamation-triangle-fill"></i> 生产环境操作确认
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger d-flex align-items-center" role="alert">
                        <i class="bi bi-shield-exclamation flex-shrink-0 me-2" style="font-size: 1.5rem;"></i>
                        <div>
                            <strong>警告：您即将在生产环境中执行操作！</strong>
                        </div>
                    </div>
                    <p class="mb-3">您当前选择的是<strong class="text-danger">生产环境</strong>，此操作将直接影响生产数据。</p>
                    <div class="bg-light p-3 rounded mb-3">
                        <h6 class="mb-2"><i class="bi bi-info-circle"></i> 操作详情：</h6>
                        <ul class="mb-0">
                            <li>环境：<strong class="text-danger">生产环境</strong></li>
                            <li>操作：上传文件并创建删除同步任务</li>
                            <li>影响：将对生产数据进行删除和同步操作</li>
                        </ul>
                    </div>
                    <p class="text-muted mb-0">
                        <small><i class="bi bi-lightbulb"></i> 建议：如果您是在测试功能，请切换到测试环境进行操作。</small>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle"></i> 取消操作
                    </button>
                    <button type="button" class="btn btn-danger" id="confirmProductionBtn">
                        <i class="bi bi-check-circle"></i> 确认在生产环境执行
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="./bootstrap.bundle.min.js"></script>
    <script>
        // 环境配置
        const ENVIRONMENTS = {
            test: {
                name: '测试环境',
                url: 'http://tax-manage-pc.dntax-test.sit.91lyd.com'
            },
            release: {
                name: 'Release环境',
                url: 'http://tax-manage-pc.servyou-release.devops.91lyd.com'
            },
            production: {
                name: '生产环境',
                url: 'https://tax-manage.dc.servyou-it.com'
            }
        };

        // 全局变量
        let API_BASE = ENVIRONMENTS.release.url; // 默认使用release环境
        const proxyIp = '**************';
        const proxyHost = proxyIp + ':3000'; // 代理服务器地址
        const vaildHost = 'http://' + proxyIp + ':6688'; // 代理服务器地址

        const HEADERS = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'X-Requested-With': 'XMLHttpRequest',
            'lineup': 'F962AAC5742B467285C2294604EF20E9'
        };

        // Cookie字符串
        const COOKIE_STRING = 'DEVICEID=F52B0923-C170-4B39-9b0c-d59590b70bfb; sso-epctoken=CE079A527667678297380BDF0FCA9A3A9632215049BE56659E75810CA641ABC01682A8889B7A810AA92C334F2046B563; sso-user=AC45B84521A3CB90DCFC1D2665B781D580DFD744814EDAAE87B19C5372FBB1803CCEB9A22289CBEB4CAA2423CBC500319B29BB4752581E90ACAAB4FDC2C9B50B1BE7FDEBAF8F6E4BA4B2DCFF8949A301F85FC8B6680A973869A63AC360FF9D2C0F5F60B835A2957A67878C76753D97A82809E8F9201EAB2A475D55D90A23E5326EB3EA8B201CD278D6900EB8605C43C0BEEC8BF514B4102BB52629AC45DC2CD9E7A6D019F8E34E50F4B200C2EE3C2ED31845E421D64D4552D8620152A799E89EC9EC5A5D8DE800B6500C6AA7208F41B97FBD3C35F4846F59235CB9D9355056A07EEBCA19D6B855A39CDE16BE0F26A6DD6A5D787361637C87F5BA6333B6D3D097CFF33CAB88D7BF80BE63B288BBF3159865AF11035722FA08CF2CB4A00C6BE17414905E06B05ACDC9A8EF1B99DAE640DDF1331B3D73150BD05B52A0916AA3A0105AA9B16280036EB8E015B620112425678DBE2BBA705B1D41839567505D0811D2728C1D4CE8579CE03882CE2881F1D00615970749D0BAEB27515ACD6272B3328F36B827AD84273B0D013DE450E1B637DF5D982FEEF61B0921A2FB7C47CF5439C4E3ED83BD4B68AE7EA75F5820A2FF6913A3B0E8B7E14AABC9EED4050D38CDA6B288D68CAA5362B19C8F4006533AD232F82922E66D49518FFF9380D11201F937C31804446D73D87CFCDB18438BFB96C25229ECAC599B8335166DA1CF00DAB686E3D2FE7F66F040345F07CD6184C12D750EDABBA0CF6098CD683D309C029A8707635AD4536743734550ECD62B79580F80226CC617B80D805279D1E01C4F0CF6D4E8249EB025C14F658C77607953B12D714EB7998D7592AD3676927E818B6AE07B23F638B36EF8CF52129C4082E32027F4404ACCBCBE77EF4DA050A6807AA8CF970440A50AD7ADF27018ECECD71D27AF5FDBFD9FAAB77E780501B9FAC501DB7A6D24E1C1E1E3ACBCB8F7FEC593AD465A7F588244D85D04C28C66B14E61250DB34221; userName=%E5%9F%BA%E7%A1%80%E8%BF%90%E8%90%A5%E5%B9%B3%E5%8F%B0%E7%AE%A1%E7%90%86%E5%91%98; home-tips=1; baseSystem-tips=1';

        // 构建代理URL的辅助函数
        function buildProxyUrl(targetUrl) {
            return `http://${proxyHost}/proxy?url=${encodeURIComponent(targetUrl)}`;
        }

        let selectedFile = null;

        // 按键序列检测相关变量
        const SECRET_SEQUENCE = ['ArrowUp', 'ArrowDown', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight'];
        let currentSequence = [];
        let isProductionUnlocked = false;

        // DOM元素
        const environmentSelect = document.getElementById('environmentSelect');
        const currentEnvironment = document.getElementById('currentEnvironment');
        const fileInput = document.getElementById('fileInput');
        const selectedFileName = document.getElementById('selectedFileName');
        const uploadBtn = document.getElementById('uploadBtn');
        const refreshBtn = document.getElementById('refreshBtn');
        const taskTableBody = document.getElementById('taskTableBody');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const messageArea = document.getElementById('messageArea');
        const taxCodeInput = document.getElementById('taxCodeInput');
        const periodInput = document.getElementById('periodInput');

        // 工具函数
        function showLoading() {
            loadingOverlay.style.display = 'flex';
        }

        function hideLoading() {
            loadingOverlay.style.display = 'none';
        }

        function validateForm() {
            const taxCode = taxCodeInput.value.trim();
            const period = periodInput.value.trim();
            const hasFile = selectedFile !== null;

            // 检查所有必填字段
            if (!taxCode) {
                showMessage('请输入taxCode', 'warning');
                taxCodeInput.focus();
                return false;
            }

            if (!period) {
                showMessage('请输入period', 'warning');
                periodInput.focus();
                return false;
            }

            // 验证期间格式（简单验证，6位数字）
            if (!/^\d{6}$/.test(period)) {
                showMessage('期间格式不正确，请输入6位数字（如：202401）', 'warning');
                periodInput.focus();
                return false;
            }

            if (!hasFile) {
                showMessage('请选择Excel文件', 'warning');
                return false;
            }

            return true;
        }

        function updateUploadButtonState() {
            const taxCode = taxCodeInput.value.trim();
            const period = periodInput.value.trim();
            const hasFile = selectedFile !== null;

            uploadBtn.disabled = !(taxCode && period && hasFile);
        }

        // 按键序列检测函数
        function handleKeyPress(event) {
            // 只在没有输入框聚焦时检测按键序列
            if (document.activeElement.tagName === 'INPUT' ||
                document.activeElement.tagName === 'TEXTAREA' ||
                document.activeElement.tagName === 'SELECT') {
                return;
            }

            const key = event.key;

            // 检查是否是序列中的按键
            if (SECRET_SEQUENCE.includes(key)) {
                currentSequence.push(key);

                // 保持序列长度不超过目标序列长度
                if (currentSequence.length > SECRET_SEQUENCE.length) {
                    currentSequence.shift();
                }

                // 检查是否匹配完整序列
                if (currentSequence.length === SECRET_SEQUENCE.length &&
                    JSON.stringify(currentSequence) === JSON.stringify(SECRET_SEQUENCE)) {

                    console.log('检测到秘密序列！');
                    currentSequence = []; // 重置序列
                    showSecretCodeModal();
                }
            } else {
                // 如果按了其他键，重置序列
                currentSequence = [];
            }
        }

        // 显示口令验证弹窗
        function showSecretCodeModal() {
            const modal = new bootstrap.Modal(document.getElementById('secretCodeModal'));
            document.getElementById('secretCodeInput').value = '';
            document.getElementById('secretCodeError').classList.add('d-none');
            modal.show();
        }

        // 验证口令
        async function verifySecretCode() {
            const secretCode = document.getElementById('secretCodeInput').value.trim();
            const errorDiv = document.getElementById('secretCodeError');

            if (!secretCode) {
                errorDiv.textContent = '请输入口令';
                errorDiv.classList.remove('d-none');
                return;
            }

            try {
                // 调用实际的API接口进行口令验证
                const response = await fetch(vaildHost + '/api/verify-secret-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ code: secretCode })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success === true) {
                    // 验证成功，显示生产环境选项
                    unlockProductionEnvironment();

                    // 关闭弹窗
                    const modal = bootstrap.Modal.getInstance(document.getElementById('secretCodeModal'));
                    modal.hide();

                    showMessage('生产环境已解锁！', 'success');
                } else {
                    // 验证失败
                    errorDiv.innerHTML = '<i class="bi bi-x-circle"></i> 口令错误，请重新输入';
                    errorDiv.classList.remove('d-none');
                    document.getElementById('secretCodeInput').value = '';
                    document.getElementById('secretCodeInput').focus();
                }
            } catch (error) {
                console.error('口令验证失败:', error);
                errorDiv.innerHTML = '<i class="bi bi-x-circle"></i> 验证失败，请稍后重试';
                errorDiv.classList.remove('d-none');
            }
        }

        // 解锁生产环境选项
        function unlockProductionEnvironment() {
            isProductionUnlocked = true;
            const productionOption = document.getElementById('productionOption');
            productionOption.style.display = 'block';

            console.log('生产环境已解锁');
        }

        function updateEnvironment() {
            const selectedEnv = environmentSelect.value;
            const envConfig = ENVIRONMENTS[selectedEnv];

            API_BASE = envConfig.url;
            currentEnvironment.textContent = envConfig.name;

            // 更新环境指示器的样式
            const indicator = document.querySelector('.environment-indicator');
            indicator.className = 'alert environment-indicator mb-0';

            switch(selectedEnv) {
                case 'test':
                    indicator.classList.add('alert-warning');
                    indicator.style.borderLeftColor = '#ffc107';
                    currentEnvironment.textContent = envConfig.name;
                    break;
                case 'production':
                    indicator.classList.add('alert-danger', 'production-warning');
                    indicator.style.borderLeftColor = '#dc3545';
                    // 更新环境指示器文本，添加警告图标
                    currentEnvironment.innerHTML = '<i class="bi bi-exclamation-triangle-fill"></i> 生产环境';
                    break;
                default: // release
                    indicator.classList.add('alert-info');
                    indicator.style.borderLeftColor = '#0d6efd';
                    currentEnvironment.textContent = envConfig.name;
            }

            console.log('环境已切换到:', envConfig.name, envConfig.url);

            // 根据环境显示不同的消息
            if (selectedEnv === 'production') {
                showMessage(`⚠️ 已切换到${envConfig.name}，请谨慎操作！所有操作将直接影响生产数据。`, 'error');
            } else {
                showMessage(`已切换到${envConfig.name}`, 'info');
            }

            // 切换环境后自动刷新任务列表
            setTimeout(() => {
                fetchTaskList(true);
            }, 500);
        }

        function showMessage(message, type = 'info') {
            const alertClass = type === 'error' ? 'alert-danger' :
                              type === 'success' ? 'alert-success' :
                              type === 'warning' ? 'alert-warning' : 'alert-info';

            messageArea.innerHTML = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }

        function formatDate(timestamp) {
            if (!timestamp) return '-';
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN');
        }

        function getStatusBadge(status) {
            switch(status) {
                case 1:
                    return '<span class="status-badge status-success">已完成</span>';
                case 0:
                    return '<span class="status-badge status-processing">处理中</span>';
                case -1:
                    return '<span class="status-badge status-failed">失败</span>';
                default:
                    return '<span class="status-badge">未知</span>';
            }
        }

        // 解析文件名，提取taxCode、period和原始文件名
        function parseFileName(fileName) {
            // 匹配格式：[taxCode-period]originalFileName
            const match = fileName.match(/^\[([^-]+)-([^\]]+)\](.+)$/);

            if (match) {
                return {
                    taxCode: match[1],
                    period: match[2],
                    originalFileName: match[3]
                };
            }

            // 如果不匹配格式，返回原始数据
            return {
                taxCode: '',
                period: '',
                originalFileName: fileName
            };
        }

        // API调用函数
        async function fetchTaskList(smooth = false) {
            try {
                if (!smooth) {
                    showLoading();
                }

                const targetUrl = `${API_BASE}/taxmanage/batchData/pageTask?taskType=99&pageNo=1&pageSize=9999`;
                const proxyUrl = buildProxyUrl(targetUrl);

                console.log('请求URL:', proxyUrl);
                console.log('目标URL:', targetUrl);

                const response = await fetch(proxyUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.head && data.head.code === '00000000') {
                    renderTaskList(data.body.result || [], smooth);
                } else {
                    throw new Error(data.head?.description || '获取任务列表失败');
                }
            } catch (error) {
                console.error('获取任务列表失败:', error);
                if (!smooth) {
                    showMessage(`获取任务列表失败: ${error.message}`, 'error');
                }
                taskTableBody.innerHTML = '<tr><td colspan="11" class="text-center text-danger">加载失败</td></tr>';
            } finally {
                if (!smooth) {
                    hideLoading();
                }
            }
        }

        // 智能刷新函数 - 检测最新任务状态
        async function smartRefresh() {
            try {
                // 静默获取任务列表，不显示loading
                const targetUrl = `${API_BASE}/taxmanage/batchData/pageTask?taskType=99&pageNo=1&pageSize=9999`;
                const proxyUrl = buildProxyUrl(targetUrl);

                const response = await fetch(proxyUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.head && data.head.code === '00000000') {
                    const newTasks = data.body.result || [];

                    // 检查是否有新任务或状态变化
                    if (hasTaskChanges(newTasks)) {
                        console.log('检测到任务变化，执行平滑刷新');
                        renderTaskList(newTasks, true);
                    } else {
                        console.log('任务无变化，跳过刷新');
                    }
                }
            } catch (error) {
                console.error('智能刷新失败:', error);
                // 静默失败，不显示错误消息
            }
        }

        // 检查任务是否有变化
        function hasTaskChanges(newTasks) {
            if (currentTasks.length !== newTasks.length) {
                return true;
            }

            // 检查每个任务的关键字段是否有变化
            for (let i = 0; i < newTasks.length; i++) {
                const newTask = newTasks[i];
                const oldTask = currentTasks.find(t => t.id === newTask.id);

                if (!oldTask) {
                    return true; // 新任务
                }

                // 检查关键字段变化
                if (oldTask.status !== newTask.status ||
                    oldTask.successNum !== newTask.successNum ||
                    oldTask.failNum !== newTask.failNum ||
                    oldTask.totalCount !== newTask.totalCount) {
                    return true;
                }
            }

            return false;
        }

        async function uploadFile(file) {
            try {
                showLoading();

                const targetUrl = `${API_BASE}/cloudpoi/api/excel/import`;
                const proxyUrl = buildProxyUrl(targetUrl);

                // 创建FormData对象
                const formData = new FormData();
                formData.append('excel', file);
                formData.append('appName', 'saas-xtax');
                formData.append('otherService', 'true');

                const response = await fetch(proxyUrl, {
                    method: 'POST',
                    // 不要设置Content-Type，让浏览器自动设置multipart/form-data和boundary
                    body: formData
                });

                console.log('上传响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.head && data.head.code === '00000000') {
                    return data.body.excelCacheId;
                } else {
                    throw new Error(data.head?.description || '文件上传失败');
                }
            } catch (error) {
                console.error('文件上传失败:', error);
                throw error;
            } finally {
                hideLoading();
            }
        }

        async function createTask(fileName, excelId, taxCode, period) {
            try {
                showLoading();
                fileName = `[${taxCode}-${period}]${fileName}`;
                const targetUrl = `${API_BASE}/taxmanage/batch/del/createTask`;
                const proxyUrl = buildProxyUrl(targetUrl);

                const response = await fetch(proxyUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fileName: fileName,
                        excelId: excelId,
                        taxCode: taxCode,
                        period: period
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.head && data.head.code === '00000000') {
                    return true;
                } else {
                    throw new Error(data.head?.description || '创建任务失败');
                }
            } catch (error) {
                console.error('创建任务失败:', error);
                throw error;
            } finally {
                hideLoading();
            }
        }

        // 存储当前任务列表数据，用于智能更新
        let currentTasks = [];

        // 渲染任务列表
        function renderTaskList(tasks, smooth = false) {
            if (!tasks || tasks.length === 0) {
                taskTableBody.innerHTML = '<tr><td colspan="11" class="text-center text-muted">暂无数据</td></tr>';
                currentTasks = [];
                return;
            }

            // 如果启用平滑模式，先添加渐变效果
            if (smooth) {
                const tableContainer = document.querySelector('.table-container');
                tableContainer.classList.add('refreshing');

                setTimeout(() => {
                    updateTaskList(tasks);
                    tableContainer.classList.remove('refreshing');
                }, 150);
            } else {
                updateTaskList(tasks);
            }
        }

        // 实际更新任务列表的函数
        function updateTaskList(tasks) {
            const rows = tasks.map(task => {
                // 解析文件名获取taxCode、period和原始文件名
                const parsedFileName = parseFileName(task.fileName || '');

                // 优先使用解析出的值，如果解析失败则使用API返回的值
                const displayTaxCode = parsedFileName.taxCode || task.taxCode || '-';
                const displayPeriod = parsedFileName.period || task.period || '-';
                const displayFileName = parsedFileName.originalFileName || task.fileName || '-';

                return `
                    <tr data-task-id="${task.id}">
                        <td>${task.id}</td>
                        <td title="${displayFileName}">${displayFileName}</td>
                        <td title="${displayTaxCode}">${displayTaxCode}</td>
                        <td>${displayPeriod}</td>
                        <td>${task.totalCount}</td>
                        <td class="text-success">${task.successNum}</td>
                        <td class="text-danger">${task.failNum}</td>
                        <td>${getStatusBadge(task.status)}</td>
                        <td title="${task.creatorName}">${task.creatorName}</td>
                        <td>${formatDate(task.createDate)}</td>
                        <td>
                            ${task.ossDownloadUrl ?
                    `<a href="${task.ossDownloadUrl}" class="btn btn-outline-primary btn-download" target="_blank" download>
                                    <i class="bi bi-download"></i> 下载
                                </a>` :
                    '<span class="text-muted">无文件</span>'
                }
                        </td>
                    </tr>
                `;
            }).join('');

            taskTableBody.innerHTML = rows;
            currentTasks = tasks;
        }

        // 事件处理函数
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // 验证文件类型
                const allowedTypes = ['.xlsx', '.xls'];
                const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

                if (!allowedTypes.includes(fileExtension)) {
                    showMessage('请选择Excel文件（.xlsx 或 .xls 格式）', 'error');
                    fileInput.value = '';
                    selectedFileName.textContent = '';
                    selectedFile = null;
                    updateUploadButtonState();
                    return;
                }

                // 验证文件大小（限制为10MB）
                const maxSize = 10 * 1024 * 1024; // 10MB
                if (file.size > maxSize) {
                    showMessage('文件大小不能超过10MB', 'error');
                    fileInput.value = '';
                    selectedFileName.textContent = '';
                    selectedFile = null;
                    updateUploadButtonState();
                    return;
                }

                selectedFile = file;
                selectedFileName.textContent = `已选择: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`;
                updateUploadButtonState();

                // 清除之前的错误消息
                messageArea.innerHTML = '';
            } else {
                selectedFile = null;
                selectedFileName.textContent = '';
                updateUploadButtonState();
            }
        });

        // 添加输入字段的事件监听器
        taxCodeInput.addEventListener('input', function() {
            updateUploadButtonState();
            // 清除错误消息
            if (this.value.trim()) {
                messageArea.innerHTML = '';
            }
        });

        periodInput.addEventListener('input', function() {
            updateUploadButtonState();
            // 清除错误消息
            if (this.value.trim()) {
                messageArea.innerHTML = '';
            }
        });

        // 环境选择事件监听器
        environmentSelect.addEventListener('change', function() {
            updateEnvironment();
        });

        // 口令验证按钮事件监听器
        document.getElementById('verifySecretCodeBtn').addEventListener('click', function() {
            verifySecretCode();
        });

        // 口令输入框回车事件
        document.getElementById('secretCodeInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                verifySecretCode();
            }
        });

        // 添加全局按键监听器
        document.addEventListener('keydown', handleKeyPress);

        uploadBtn.addEventListener('click', async function() {
            // 验证表单
            if (!validateForm()) {
                return;
            }

            // 检查是否为生产环境，需要二次确认
            if (environmentSelect.value === 'production') {
                const modal = new bootstrap.Modal(document.getElementById('productionConfirmModal'));
                modal.show();
                return;
            }

            // 执行上传操作
            await performUpload();
        });

        // 生产环境确认按钮事件
        document.getElementById('confirmProductionBtn').addEventListener('click', async function() {
            const modal = bootstrap.Modal.getInstance(document.getElementById('productionConfirmModal'));
            modal.hide();

            // 执行上传操作
            await performUpload();
        });

        // 提取上传逻辑为独立函数
        async function performUpload() {
            const taxCode = taxCodeInput.value.trim();
            const period = periodInput.value.trim();

            try {
                // 上传文件
                showMessage('正在上传文件...', 'info');
                const excelId = await uploadFile(selectedFile);

                // 创建任务
                showMessage('正在创建任务...', 'info');
                await createTask(selectedFile.name, excelId, taxCode, period);

                showMessage('文件上传并创建任务成功！', 'success');

                // 重置表单
                fileInput.value = '';
                selectedFileName.textContent = '';
                taxCodeInput.value = '';
                periodInput.value = '';
                selectedFile = null;
                updateUploadButtonState();

                // 智能刷新任务列表 - 减少闪烁
                setTimeout(() => {
                    smartRefresh();
                }, 500);

                // 备用刷新 - 确保数据更新
                setTimeout(() => {
                    fetchTaskList(true);
                }, 2000);

            } catch (error) {
                showMessage(`操作失败: ${error.message}`, 'error');
            }
        }

        refreshBtn.addEventListener('click', function() {
            fetchTaskList(true); // 使用平滑刷新
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateEnvironment(); // 初始化环境显示
            fetchTaskList(true);
        });

        // 定期智能刷新任务列表（每30秒）
        setInterval(smartRefresh, 30000);
    </script>
</body>
</html>