<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>独享</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            background-color: #f5f5f5;
        }

        .container {
            width: 80%;
            max-width: 1200px;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .header {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 20px;
            gap: 20px; /* Ensures consistent spacing between elements */
        }

        .header select,
        .header input,
        .header button {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .header select {
            width: 150px; /* Set fixed width for the select dropdown */
        }

        .header input {
            flex: 1; /* Input takes available space */
            max-width: 250px; /* Optional: restricts max width for better layout */
        }

        .header button {
            background-color: #007BFF;
            color: #fff;
            border: none;
            cursor: pointer;
            padding: 8px 20px;
        }

        .header button:hover {
            background-color: #0056b3;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }

        .delete-btn {
            padding: 5px 10px;
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .delete-btn:hover {
            background-color: #c82333;
        }
    </style>
</head>

<body>
    <input id="hiddenTaxNo" type="hidden">
    <input id="proxyHost" type="hidden" value="127.0.0.1:3000">
    <div class="container">
        <div class="header">
            <!-- 环境选择下拉框 -->
            <select id="envSelect">
                <option value="taxlineup.ntax-test.sit.91lyd.com">Test</option>
                <option value="taxlineup.servyou-release.devops.91lyd.com">Release</option>
                <option value="************">Pre</option>
                <option value="************">Prod</option>
            </select>
            <input type="text" id="taxNo" placeholder="Enter Tax No">
            <button id="queryBtn">Query</button>
        </div>
        <table id="resultTable">
            <thead>
                <tr>
                    <th>Key</th>
                    <th>Value</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                <!-- Data will be dynamically inserted here -->
            </tbody>
        </table>
    </div>
    <script>
    var proxyHost = document.getElementById("proxyHost").value;


    // 获取环境选择的URL
    function getLineupHost() {
        const envSelect = document.getElementById("envSelect");
        const selectedEnv = envSelect.value;
        return selectedEnv;
    }

    async function getEnvIP(envDomain) {
        const lineupHostUrl = encodeURIComponent('http://phoenix.dc.servyou-it.com/phoenix/ops/getApplyOnlineCluster?appCode=taxlineup');
        const url = `http://${proxyHost}/proxy?url=${lineupHostUrl}`;
        // try {
        //     await fetch(url)
        //         .then(response => response.json())
        //         .then(data => {
        //             // console.log(data.body)
        //             // 根据传入的 envDomain 获取对应的 IP 地址
        //             const envData = data.body.filter(x => x.envDomain === envDomain)[0];
        //             if (envData && envData.clusterList[0] && envData.clusterList[0].k8sPodInfos[0]) {
        //                 console.log(envData.clusterList[0].k8sPodInfos[0].podIp)
        //                 return envData.clusterList[0].k8sPodInfos[0].podIp;
        //             } else {
        //                 throw new Error(`IP not found for ${envDomain}`);
        //             }
        //         })
        //         .catch(error => {
        //             console.error('请求出错:', error);
        //         });


        // } catch (error) {
        //     console.error(`Failed to get IP for ${envDomain}:`, error);
        //     return null;
        // }
        try {
            const response = await fetch(url);
            const data = await response.json();

            // 根据传入的 envDomain 获取对应的 IP 地址
            const envData = data.body.filter(x => x.envDomain === envDomain)[0];
            if (envData && envData.clusterList[0] && envData.clusterList[0].k8sPodInfos[0]) {
                return envData.clusterList[0].k8sPodInfos[0].podIp;
            } else {
                throw new Error(`IP not found for ${envDomain}`);
            }
        } catch (error) {
            console.error(`Failed to get IP for ${envDomain}:`, error);
            return null;
        }
        console.log(1111)
    }
    async function updateEnvSelect() {

        // 获取 pre 和 prod 的动态 IP 地址
        const preIp = await getEnvIP('pre');
        const prodIp = await getEnvIP('prod');
        console.log(preIp)
        console.log(prodIp)


        // 如果成功获取到 IP 地址，更新下拉框中的值
        if (preIp && prodIp) {
            const envSelect = document.getElementById('envSelect');
            const preOption = envSelect.querySelector('option[value="************"]');
            const prodOption = envSelect.querySelector('option[value="************"]');

            // 更新 pre 和 prod 的选项
            if (preOption) {
                preOption.value = preIp+":8080";
                preOption.textContent = `Pre (${preIp})`;
            }
            if (prodOption) {
                prodOption.value = prodIp+":8080";
                prodOption.textContent = `Prod (${prodIp})`;
            }
        }
    }
    window.addEventListener('load', () => {
        updateEnvSelect();
    });



    document.getElementById('queryBtn').addEventListener('click', async () => {
        const taxNo = document.getElementById('taxNo').value.trim();
        if (!taxNo) {
            alert('Please enter a Tax No');
            return;
        }

        document.getElementById('hiddenTaxNo').value = taxNo;

        const lineupHost = getLineupHost(); // 获取当前选择的环境
        const url = `http://${proxyHost}/proxy?url=http://${lineupHost}/taxlineup/task/test/queryOccupy`;
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ taxNo })
        });

        const result = await response.json();
        const tableBody = document.getElementById('resultTable').querySelector('tbody');
        tableBody.innerHTML = '';

        if (result.body) {
            let firstRow = true;
            for (const [key, value] of Object.entries(result.body)) {
                const row = document.createElement('tr');

                // Key Column
                const keyCell = document.createElement('td');
                keyCell.textContent = key;
                row.appendChild(keyCell);

                // Value Column
                const valueCell = document.createElement('td');
                try {
                    const parsedValue = JSON.parse(value);
                    if (typeof parsedValue === 'object') {
                        valueCell.innerHTML = '<table>' + Object.entries(parsedValue)
                            .map(([k, v]) => {
                                if (k === 'timeout') {

                                    // 获取 startTime 和 timeout 的值
                                    const startTimeInSeconds = Math.floor((parsedValue.startTime || 0) / 1000); // 将 startTime 转为秒
                                    const currentTimeInSeconds = Math.floor(Date.now() / 1000); // 当前时间戳（秒）
                                    const remainingTime = Math.max(v - currentTimeInSeconds + startTimeInSeconds, 0); // 计算剩余时间（秒）

                                    // 倒计时部分
                                    return `
                                                <tr>
                                                    <td>${k}</td>
                                                    <td>
                                                        ${v} <span style="color: red;">(${remainingTime} 秒)</span>
                                                    </td>
                                                </tr>
                                            `;
                                } else {
                                    return `<tr><td>${k}</td><td>${v}</td></tr>`;
                                }
                            })
                            .join('') + '</table>';
                        // 定义倒计时刷新逻辑
                        const updateCountdown = () => {
                            const currentTimeInSeconds = Math.floor(Date.now() / 1000); // 当前时间戳（秒）
                            const rows = valueCell.querySelectorAll('tr');
                            rows.forEach(row => {
                                const cells = row.querySelectorAll('td');
                                if (cells[0] && cells[0].textContent === 'timeout') {
                                    const timeoutValue = parseInt(cells[1].textContent, 10); // 读取 timeout 原始值
                                    const startTimeInSeconds = Math.floor((parsedValue.startTime || 0) / 1000); // 转为秒
                                    const remainingTime = Math.max(timeoutValue - currentTimeInSeconds + startTimeInSeconds, 0); // 计算剩余时间（秒）
                                    const redText = `<span style="color: red;">(${remainingTime} 秒)</span>`;
                                    cells[1].innerHTML = `${timeoutValue} ${redText}`;
                                }
                            });
                        };
                        setInterval(updateCountdown, 1000);

                    } else {
                        valueCell.textContent = parsedValue;
                    }
                } catch {
                    valueCell.textContent = value;
                }
                row.appendChild(valueCell);

                // Action Column
                const actionCell = document.createElement('td');
                if (firstRow) {
                    const deleteTaxNo = document.getElementById('hiddenTaxNo').value;
                    let obj = {
                        taxNo: deleteTaxNo
                    };
                    const deleteBtn = document.createElement('button');
                    deleteBtn.textContent = 'Delete';
                    deleteBtn.className = 'delete-btn';
                    deleteBtn.addEventListener('click', async () => {
                        const deleteUrl = `http://${proxyHost}/proxy?url=http://${lineupHost}/taxlineup/task/test/facade/cancelOccupy`;
                        await fetch(deleteUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(obj)
                        });
                        document.getElementById('queryBtn').click();
                    });
                    actionCell.appendChild(deleteBtn);
                    firstRow = false;
                }
                row.appendChild(actionCell);

                tableBody.appendChild(row);
            }
        } else {
            const noDataRow = document.createElement('tr');
            const noDataCell = document.createElement('td');
            noDataCell.colSpan = 3;
            noDataCell.textContent = 'No data found';
            noDataRow.appendChild(noDataCell);
            tableBody.appendChild(noDataRow);
        }
    });
    </script>
</body>

</html>