<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排队日志查询</title>
    <link href="./bootstrap.min.css" rel="stylesheet">
    <style>
        /* 现代化页面布局 */
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            color: #2c3e50;
        }

        /* 页面头部区域 */
        .page-header {
            background: white;
            border-bottom: 1px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            padding: 20px 0;
            position: relative;
            z-index: 100;
        }

        .page-title {
            color: #2c3e50;
            font-size: 28px;
            font-weight: 600;
            margin: 0 0 20px 0;
            text-align: center;
        }

        /* 查询表单美化 */
        .query-form {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #e9ecef;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
            background: white;
            outline: none;
        }

        /* 环境选择下拉框美化 */
        .dropdown-toggle {
            background: #fafbfc !important;
            border: 2px solid #e5e7eb !important;
            color: #374151 !important;
            border-radius: 8px !important;
            padding: 12px 16px !important;
            font-size: 14px !important;
            transition: all 0.3s ease !important;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .dropdown-toggle:hover,
        .dropdown-toggle:focus {
            border-color: #3498db !important;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1) !important;
            background: white !important;
        }

        .dropdown-menu {
            border: none !important;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
            border-radius: 12px !important;
            padding: 8px !important;
            margin-top: 8px !important;
        }

        .dropdown-item {
            border-radius: 8px !important;
            padding: 10px 16px !important;
            margin: 2px 0 !important;
            transition: all 0.2s ease !important;
            font-size: 14px !important;
        }

        .dropdown-item:hover {
            background: #3498db !important;
            color: white !important;
            transform: translateY(-1px);
        }

        /* 查询按钮美化 */
        .btn-primary {
            background: #3498db;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
            background: #2980b9;
        }

        /* 表格容器美化 */
        .table-container {
            flex: 1;
            margin: 20px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
        }

        .table-wrapper {
            flex: 1;
            overflow: auto;
            position: relative;
        }

        /* 表格样式美化 */
        .table {
            width: 100%;
            margin: 0;
            border-collapse: collapse;
            font-size: 13px;
        }

        .table thead th {
            position: sticky;
            top: 0;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            font-weight: 600;
            font-size: 12px;
            /*text-transform: uppercase;*/
            text-align: center;
            letter-spacing: 0.5px;
            padding: 16px 12px;
            border: none;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
        }

        .table tbody tr {
            transition: all 0.2s ease;
            border-bottom: 1px solid #f1f3f4;
        }

        .table tbody tr:hover {
            background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(52, 73, 94, 0.15);
        }

        .table tbody tr:nth-child(even) {
            background-color: rgba(248, 249, 250, 0.5);
        }

        .table tbody tr:nth-child(even):hover {
            background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
        }

        /* 表格行分隔线 */
        .table tbody tr {
            border-bottom: 1px solid rgba(240, 240, 240, 0.8);
        }

        /* 表格单元格内边距调整 */
        .table td {
            padding: 8px 6px;
            vertical-align: top;
        }

        /* 消息内容特殊标记样式 */
        .message-column [style*="background"] {
            border-radius: 4px;
            padding: 4px 8px;
            margin: 1px 0;
            display: block;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .table td {
            padding: 12px;
            border: none;
            vertical-align: middle;
        }

        /* 消息列特殊样式 */
        .message-column {
            width: 55%;
            word-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            line-height: 1.5;
            font-size: 12px;
            position: relative;
            border-radius: 6px;
            margin: 2px;
        }

        .server-column {
            width: 9%;
            word-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            font-size: 11px;
            text-align: center;
        }

        /* 链接样式美化 */
        .table a {
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            border-radius: 6px;
            padding: 3px 8px;
            background: rgba(52, 152, 219, 0.08);
            border: 1px solid rgba(52, 152, 219, 0.15);
        }

        .table a:hover {
            background: rgba(52, 152, 219, 0.15);
            color: #2980b9;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 3px 6px rgba(52, 152, 219, 0.25);
        }

        /* ticket链接特殊样式 */
        .ticket-link {
            background: rgba(46, 204, 113, 0.08) !important;
            border-color: rgba(46, 204, 113, 0.2) !important;
            color: #27ae60 !important;
        }

        .ticket-link:hover {
            background: rgba(46, 204, 113, 0.15) !important;
            color: #229954 !important;
            box-shadow: 0 3px 6px rgba(46, 204, 113, 0.25) !important;
        }

        /* 时间列样式 */
        .table td:first-child {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            font-size: 11px;
            color: #666;
            white-space: nowrap;
        }

        /* taskId和traceId列样式 */
        .table td:nth-child(2),
        .table td:nth-child(3) {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            font-size: 11px;
            color: #333;
        }

        /* 服务器链接样式 */
        .table td:nth-child(5) a {
            background: rgba(230, 126, 34, 0.08) !important;
            border-color: rgba(230, 126, 34, 0.2) !important;
            color: #e67e22 !important;
        }

        .table td:nth-child(5) a:hover {
            background: rgba(230, 126, 34, 0.15) !important;
            color: #d35400 !important;
            box-shadow: 0 3px 6px rgba(230, 126, 34, 0.25) !important;
        }

        /* 自定义滚动条 */
        .table-wrapper::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .table-wrapper::-webkit-scrollbar-track {
            background: #f1f3f4;
            border-radius: 4px;
        }

        .table-wrapper::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            border-radius: 4px;
        }

        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
        }

        .table-wrapper::-webkit-scrollbar-corner {
            background: #f1f3f4;
        }

        /* 模态框美化 */
        .modal-dialog {
            max-width: 70%;
        }

        .modal-content {
            border: none;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .modal-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            border: none;
            padding: 20px 24px;
            position: relative;
            overflow: hidden;
        }

        .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3498db 0%, #27ae60 50%, #e67e22 100%);
        }

        .modal-title {
            font-weight: 600;
            font-size: 18px;
            display: flex;
            align-items: center;
            margin: 0;
        }

        .modal-title::before {
            content: '📋';
            margin-right: 10px;
            font-size: 20px;
        }

        .btn-close {
            filter: invert(1);
            opacity: 0.8;
            transition: all 0.3s ease;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-close:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(90deg);
        }

        .modal-body {
            padding: 0;
            max-height: 60vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .modal-content-header {
            padding: 16px 24px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-size: 13px;
            color: #6c757d;
            font-weight: 500;
        }

        .modal-body pre {
            background: #2d3748;
            color: #e2e8f0;
            border: none;
            border-radius: 0;
            padding: 20px 24px;
            font-size: 12px;
            line-height: 1.6;
            margin: 0;
            flex: 1;
            overflow-y: auto;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
        }

        /* JSON语法高亮 */
        .modal-body pre .json-key {
            color: #63b3ed;
        }

        .modal-body pre .json-string {
            color: #68d391;
        }

        .modal-body pre .json-number {
            color: #fbb6ce;
        }

        .modal-body pre .json-boolean {
            color: #f6ad55;
        }

        .modal-body pre .json-null {
            color: #a0aec0;
        }

        .modal-footer {
            border: none;
            padding: 16px 24px;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-footer .btn-secondary {
            background: #6c757d;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .modal-footer .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .modal-info {
            font-size: 12px;
            color: #6c757d;
            display: flex;
            align-items: center;
        }

        .modal-info::before {
            content: 'ℹ️';
            margin-right: 6px;
        }

        /* 加载动画美化 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.2);
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        .loading-text {
            color: white;
            font-size: 16px;
            font-weight: 500;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 雪花动画 */
        .snowflake {
            position: absolute;
            top: -10px;
            font-size: 1em;
            color: rgba(255, 255, 255, 0.8);
            opacity: 0.9;
            animation: fall linear infinite;
            z-index: 11;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        @keyframes fall {
            0% { transform: translateY(0) rotate(0deg); }
            100% { transform: translateY(100vh) rotate(360deg); }
        }

        /* 页面布局 */
        .page-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .content-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .page-title {
                font-size: 24px;
            }

            .query-form {
                padding: 16px;
                border-radius: 12px;
            }

            .table-container {
                margin: 10px;
                border-radius: 12px;
            }

            .modal-dialog {
                max-width: 95%;
                margin: 10px;
            }
        }
    </style>
</head>

<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="container-fluid">
                <h1 class="page-title">排队日志查询系统</h1>
                <div class="query-form">
                    <div class="row g-3">
                        <div class="col" style="display: none" id="proxyConfigDiv">
                            <label for="proxyConfig" class="form-label">代理IP</label>
                            <input type="text" class="form-control" id="proxyConfig" value="**************:3000">
                        </div>
                        <div class="col">
                            <label for="env" class="form-label">环境</label>
                            <div class="dropdown">
                                <button class="btn btn-secondary dropdown-toggle" type="button" id="envDropdown" data-bs-toggle="dropdown" aria-expanded="false" style="width: 100%;">
                                    prod
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="envDropdown" style="width: 100%;">
                                    <li><a class="dropdown-item" href="#" onclick="selectEnv('prod')">prod</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="selectEnv('pre')">pre</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="selectEnv('release')">release</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="selectEnv('test')">test</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="col">
                            <label for="traceId" class="form-label">TraceId</label>
                            <input type="text" class="form-control" id="traceId">
                        </div>
                        <div class="col">
                            <label for="taskId" class="form-label">TaskId</label>
                            <input type="text" class="form-control" id="taskId">
                        </div>
                        <div class="col">
                            <label for="startTime" class="form-label">时间起</label>
                            <input type="datetime-local" class="form-control" id="startTime">
                        </div>
                        <div class="col">
                            <label for="endTime" class="form-label">时间止</label>
                            <input type="datetime-local" class="form-control" id="endTime">
                        </div>
                        <div class="col">
                            <label for="keyword" class="form-label">关键词</label>
                            <input type="text" class="form-control" id="keyword" list="keywordOptions">
                            <datalist id="keywordOptions">
                                <option value="凭证校验checkTicket">
                                <option value="获取凭证开始">
                                <option value="任务落库成功">
                                <option value="生成凭证">
                                <option value="发送业务通知MQ[业务]">
                                <option value="调用方">
                            </datalist>
                        </div>
                        <div class="col">
                            <label for="operate" class="form-label">操作</label>
                            <div class="d-grid">
                                <button id="queryButton" class="btn btn-primary">查询</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-container">
            <div class="table-container">
                <div class="table-wrapper">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th scope="col" style="width: 10%;">time</th>
                                <th scope="col" style="width: 5%">taskId</th>
                                <th scope="col" style="width: 5%">traceId</th>
                                <th scope="col" style="width: 55%">event</th>
                                <th scope="col" style="width: 10%">sourceHost</th>
                                <th scope="col" style="width: 10%">clusterName</th>
                            </tr>
                        </thead>
                        <tbody id="log-data">
                            <!-- 数据行将由JavaScript渲染 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 模态框 -->
    <div class="modal fade" id="responseModal" tabindex="-1" aria-labelledby="responseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="responseModalLabel">任务详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
<!--                    <div class="modal-content-header">-->
<!--                        JSON 格式数据 - 可滚动查看完整内容-->
<!--                    </div>-->
                    <pre id="modalContent"></pre>
                </div>
                <div class="modal-footer">
                    <div class="modal-info">
                        数据来源：排队系统
                    </div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="spinner"></div>
        <div class="loading-text">正在查询日志...</div>
    </div>

    <script src="./bootstrap.bundle.min.js"></script>
    <script>
        // 创建雪花的函数
        function createSnowflake() {
            const snowflake = document.createElement('div');
            snowflake.classList.add('snowflake');
            snowflake.textContent = '无bug';

            // 随机设置雪花的初始位置和大小
            snowflake.style.left = Math.random() * 100 + 'vw';
            snowflake.style.animationDuration = Math.random() * 9 + 4 + 's';
            snowflake.style.fontSize = Math.random() * 10 + 10 + 'px';

            document.body.appendChild(snowflake);

            // 雪花下落完后移除
            setTimeout(() => {
                snowflake.remove();
            }, 5000);
        }

        function selectEnv(env) {
            document.getElementById('envDropdown').textContent = env;
        }

        let wardenDomainMap = new Map();
        wardenDomainMap.set('prod', 'https://warden.dc.servyou-it.com');
        wardenDomainMap.set('pre', 'https://warden.dc.servyou-it.com');
        wardenDomainMap.set('release', 'http://warden-eyes-front.servyou-stable.sit.91lyd.com');
        wardenDomainMap.set('test', 'http://warden-eyes-front.servyou-stable.sit.91lyd.com');

        let taxmanageMap = new Map();
        taxmanageMap.set('prod', 'http://tax-manage.dc.servyou-it.com');
        taxmanageMap.set('pre', 'http://pre-tax-manage.dc.servyou-it.com');
        taxmanageMap.set('release', 'http://tax-manage-pc.servyou-release.devops.91lyd.com');
        taxmanageMap.set('test', 'http://tax-manage-pc.dntax-test.sit.91lyd.com');

        let messageUrl = new Map();
        messageUrl.set('test', 'http://governor-ui.servyou-stable.sit.91lyd.com/rocketMQ/messageManager?topic=test%taxlineup-topic-biz-notify&key=replaceKey&type=1');
        messageUrl.set('release', 'http://governor-ui.servyou-stable.sit.91lyd.com/rocketMQ/messageManager?topic=release%taxlineup-topic-biz-notify&key=replaceKey&type=1');
        messageUrl.set('pre', 'http://governor.hz.servyou-it.com/rocketMQ/messageManager?topic=pre%taxlineup-topic-biz-notify&key=replaceKey&type=1');
        messageUrl.set('prod', 'http://governor.hz.servyou-it.com/rocketMQ/messageManager?topic=prod%taxlineup-topic-biz-notify&key=replaceKey&type=1');

        const logTableBody = document.getElementById('log-data');
        var currentDisplay = false;

        // 目标序列: 上 下 上 下 左 右 左 右 A B A B
        const targetSequence = ['ArrowUp', 'ArrowDown', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyA', 'KeyB', 'KeyA', 'KeyB'];
        let currentSequence = [];
        const targetSequence2 = ['ArrowDown', 'ArrowDown', 'ArrowDown'];
        let currentSequence2 = [];

        // 启动雪花生成
        function startSnow() {
            snowflakeInterval = setInterval(createSnowflake, 100);
        }

        // 停止雪花生成
        function stopSnow() {
            clearInterval(snowflakeInterval);
        }
        var snowFlag = false;

        function switchSnow() {
            if (snowFlag) {
                snowFlag = false;
                stopSnow();
            } else {
                snowFlag = true;
                startSnow();
            }
        }

        // 监听按键事件
        document.addEventListener('keydown', function(event) {
            const key = event.code;

            // 将按键加入到当前序列
            currentSequence.push(key);
            currentSequence2.push(key);

            // 检查当前序列是否超出目标长度
            if (currentSequence.length > targetSequence.length) {
                currentSequence.shift();
            }
            if (currentSequence2.length > targetSequence2.length) {
                currentSequence2.shift();
            }

            // 比较当前序列和目标序列
            if (currentSequence.join('') === targetSequence.join('')) {
                document.getElementById('proxyConfigDiv').style.display = currentDisplay ? 'none' : 'block';
                currentDisplay = !currentDisplay;
                currentSequence = [];
            }

            if (currentSequence2.join('') === targetSequence2.join('')) {
                switchSnow();
                currentSequence2 = [];
            }
        });

        function formatTimestamp(timestamp, gap, needMillis) {
            const date = new Date(timestamp + gap * 60 * 1000);

            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            const milliseconds = String(date.getMilliseconds()).padStart(3, '0');

            if (needMillis) {
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
            } else {
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            }
        }

        function formatyyyyMMddHHmmss(timestamp) {
            const date = new Date(timestamp);

            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}${month}${day}${hours}${minutes}${seconds}`;
        }

        function showLoading() {
            document.getElementById("loadingOverlay").style.display = "flex";
        }

        function hideLoading() {
            document.getElementById("loadingOverlay").style.display = "none";
        }

        function getMessageUrl(taskId) {
            const env = document.getElementById('envDropdown').textContent.trim();
            var tempmessageUrl = messageUrl.get(env);
            return tempmessageUrl.replace('replaceKey', taskId);
        }

        // JSON语法高亮函数
        function highlightJSON(json) {
            if (typeof json !== 'string') {
                json = JSON.stringify(json, null, 2);
            }

            return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
                var cls = 'json-number';
                if (/^"/.test(match)) {
                    if (/:$/.test(match)) {
                        cls = 'json-key';
                    } else {
                        cls = 'json-string';
                    }
                } else if (/true|false/.test(match)) {
                    cls = 'json-boolean';
                } else if (/null/.test(match)) {
                    cls = 'json-null';
                }
                return '<span class="' + cls + '">' + match + '</span>';
            });
        }

        // 渲染数据到表格
        function renderData(data) {
            logTableBody.innerHTML = '';
            data.forEach(log => {
                const row = document.createElement('tr');
                var msgStyle = '';

                // 根据消息内容设置背景色
                if (log.fields.msg.includes('凭证校验checkTicket')) {
                    msgStyle = 'style="background: linear-gradient(90deg, #d5f4e6 0%, #c8e6d0 100%); border-left: 3px solid #27ae60;"';
                } else if (log.fields.msg.includes('获取凭证开始')) {
                    msgStyle = 'style="background: linear-gradient(90deg, #d6eaf8 0%, #aed6f1 100%); border-left: 3px solid #3498db;"';
                } else if (log.fields.msg.includes('任务落库成功')) {
                    msgStyle = 'style="background: linear-gradient(90deg, #fdeaa7 0%, #f7dc6f 100%); border-left: 3px solid #f39c12;"';
                } else if (log.fields.msg.includes('生成凭证')) {
                    msgStyle = 'style="background: linear-gradient(90deg, #e8daef 0%, #d7bde2 100%); border-left: 3px solid #9b59b6;"';
                } else if (log.fields.msg.includes('发送业务通知MQ[业务]')) {
                    msgStyle = 'style="background: linear-gradient(90deg, #fadbd8 0%, #f1948a 100%); border-left: 3px solid #e74c3c;"';
                } else if (log.fields.msg.includes('调用方')) {
                    msgStyle = 'style="background: linear-gradient(90deg, #eaeded 0%, #d5dbdb 100%); border-left: 3px solid #95a5a6;"';
                } else {
                    msgStyle = 'style="background: linear-gradient(90deg, #fdfefe 0%, #f8f9fa 100%);"';
                }

                var msgUrl = getMessageUrl(log.fields.taskId);

                row.innerHTML = `
                    <td class="server-column">${formatTimestamp(log.timestamp, 0, true)}</td>
                    <td class="server-column">${log.fields.taskId === '>>>>>>' || log.fields.taskId === '000000' ? log.fields.taskId : `<a href='#' class='ticket-link' data-taskId='${log.fields.taskId}' data-time='${log.timestamp}'>${log.fields.taskId}</a>`}</td>
                    <td class="server-column">${log.fields.traceId}</td>
                    <td class="message-column" ${msgStyle}>
                    ${log.fields.msg ? log.fields.msg.replace(/("ticket":"(.*?)"|ticket=([a-zA-Z0-9\-_=]+))/g, (match, p1, p2, p3) => {
                        const ticketValue = p2 || p3;
                        return `"ticket":"<a href='#' class='ticket-link' data-ticket='${ticketValue}' data-time='${log.timestamp}'>${ticketValue}</a>"`;
                    }).replace(/code=000000/g, '<a href=' + msgUrl + ' target="_blank">code=000000[可执行通知]</a>')
                      .replace(/code=000001/g, '<a href=' + msgUrl + ' target="_blank">code=000001[执行超时]</a>')
                      .replace(/code=000002/g, '<a href=' + msgUrl + ' target="_blank">code=000002[排队超时]</a>')
                      .replace(/code=000003/g, 'code=000003[主动取消]')
                      .replace(/code=000004/g, '<a href=' + msgUrl + ' target="_blank">code=000004[强制被中断]</a>') : ""}
                    </td>
                    <td class="server-column"><a href='http://phoenix.dc.servyou-it.com/v2/#/ssh/terminal?appCode=taxlineup&podName=${log.fields['@sourceHost']}' target="_blank">${log.fields['@sourceHost']}</a></td>
                    <td class="server-column">${log.fields['@clusterName']}</td>
                `;
                logTableBody.appendChild(row);
            });

            // 为ticket链接添加事件监听器
            document.querySelectorAll('.ticket-link').forEach(link => {
                link.addEventListener('click', function(event) {
                    event.preventDefault();
                    const ticketValue = this.getAttribute('data-ticket');
                    const taskId = this.getAttribute('data-taskId');
                    const createTime = Number(this.getAttribute('data-time'));
                    const env = document.getElementById('envDropdown').textContent.trim();
                    let targetUrl = '';

                    if (taskId) {
                        targetUrl = encodeURIComponent(`${taxmanageMap.get(env)}/taxmanage/taxlineup/task/query?startCreateTime=${formatTimestamp(createTime, -60, false)}&endCreateTime=${formatTimestamp(createTime, 30, false)}&taskId=${taskId}&currentPage=1&size=9999`);
                    } else {
                        targetUrl = encodeURIComponent(`${taxmanageMap.get(env)}/taxmanage/taxlineup/task/query?startCreateTime=${formatTimestamp(createTime, -60, false)}&endCreateTime=${formatTimestamp(createTime, 30, false)}&ticket=${ticketValue}&currentPage=1&size=9999`);
                    }

                    const proxyHost = document.getElementById('proxyConfig').value.trim();
                    const url = `http://${proxyHost}/proxy?url=` + targetUrl;

                    // 发送GET请求
                    fetch(url)
                        .then(response => response.json())
                        .then(data => {
                            const jsonString = JSON.stringify(data.body.result[0], null, 2);
                            document.getElementById('modalContent').innerHTML = highlightJSON(jsonString);
                            document.getElementById('responseModalLabel').textContent = taskId ? `任务详情 - ${taskId}` : `凭证详情 - ${ticketValue}`;
                            const modal = new bootstrap.Modal(document.getElementById('responseModal'));
                            modal.show();
                        })
                        .catch(error => {
                            console.error('请求出错:', error);
                            document.getElementById('modalContent').innerHTML = '<span class="json-string">请求失败，请稍后重试。</span>';
                            document.getElementById('responseModalLabel').textContent = '错误信息';
                            const modal = new bootstrap.Modal(document.getElementById('responseModal'));
                            modal.show();
                        });
                });
            });
            hideLoading();
        }

        // 查询按钮事件处理
        document.getElementById('queryButton').addEventListener('click', () => {
            logTableBody.innerHTML = '';
            showLoading();

            const traceId = document.getElementById('traceId').value.trim();
            const taskId = document.getElementById('taskId').value.trim();
            const startTime = formatyyyyMMddHHmmss(new Date(document.getElementById('startTime').value));
            const endTime = formatyyyyMMddHHmmss(new Date(document.getElementById('endTime').value + ':59.999'));
            const keyword = document.getElementById('keyword').value.trim();
            const env = document.getElementById('envDropdown').textContent.trim();

            let query = '@source:"/usr/local/logs/taxlineup/common-schedule.log" AND @sourceHost:*' + env + '*';

            if (taskId && taskId.trim()) {
                query += ' AND ';
                query += `taskId:"${taskId}"`;
            }

            if (traceId && traceId.trim()) {
                query += ' AND ';
                query += `traceId:"${traceId}"`;
            }

            if (keyword && keyword.trim()) {
                query += ' AND ';
                query += `msg:("${keyword}")`;
            }

            const wardenTargetUrl = encodeURIComponent(`${wardenDomainMap.get(env)}/warden/searchLog/queryLog.json?indexId=taxlineup-preset&query=${encodeURIComponent(query)}&timeScale=*********&orderByField=@timestamp&orderByType=DESC&limit=1000&userName=gufl&startTime=${startTime}&endTime=${endTime}`);
            const proxyHost = document.getElementById('proxyConfig').value.trim();
            const wardenUrl = `http://${proxyHost}/proxy?url=${wardenTargetUrl}`;

            console.log(wardenUrl);
            fetch(wardenUrl)
                .then(response => response.json())
                .then(data => {
                    var firstData = data.body.logList;
                    console.log(firstData.length);

                    // 处理返回数据并渲染到表格
                    if (!keyword && taskId && taskId.trim() !== '' && Array.isArray(firstData) && firstData.length > 0) {
                        const filteredLogs = data.body.logList.filter(log => log.fields.msg.includes('任务落库成功'));
                        if (filteredLogs.length > 0) {
                            const traceId2 = filteredLogs[0].fields.traceId;
                            let query2 = '@source:"/usr/local/logs/taxlineup/common-schedule.log"';

                            if (traceId2 && traceId2.trim()) {
                                query2 += ' AND ';
                                query2 += `traceId:"${traceId2}"`;
                            }

                            const wardenTargetUrl2 = encodeURIComponent(`${wardenDomainMap.get(env)}/warden/searchLog/queryLog.json?indexId=taxlineup-preset&query=${encodeURIComponent(query2)}&timeScale=*********&orderByField=@timestamp&orderByType=DESC&limit=1000&userName=gufl&startTime=${startTime}&endTime=${endTime}`);
                            const wardenUrl2 = `http://${proxyHost}/proxy?url=${wardenTargetUrl2}`;

                            console.log(wardenUrl2);
                            fetch(wardenUrl2)
                                .then(response2 => response2.json())
                                .then(data2 => {
                                    console.log(data2.body.logList.length);
                                    const mergedArray = firstData.concat(data2.body.logList);
                                    const totalData = Array.from(new Map(mergedArray.map(item => [item._id, item])).values());
                                    totalData.sort((a, b) => b.timestamp - a.timestamp);

                                    console.log(totalData.length);
                                    renderData(totalData);
                                })
                                .catch(error => {
                                    console.error('请求出错:', error);
                                    hideLoading();
                                });
                        } else {
                            renderData(data.body.logList);
                        }
                    } else {
                        renderData(data.body.logList);
                    }
                })
                .catch(error => {
                    console.error('请求出错:', error);
                    hideLoading();
                });
        });

        // 初始化时间输入框的默认值
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
            const fiveMinutesLater = new Date(now.getTime() + 5 * 60 * 1000);
            const options = { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', hour12: false };

            document.getElementById('startTime').value = fiveMinutesAgo.toLocaleString('sv-SE', options).replace(' ', 'T');
            document.getElementById('endTime').value = fiveMinutesLater.toLocaleString('sv-SE', options).replace(' ', 'T');
        });
    </script>
</body>

</html>